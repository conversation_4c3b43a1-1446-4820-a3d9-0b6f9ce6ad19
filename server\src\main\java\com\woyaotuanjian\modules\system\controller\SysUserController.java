package com.woyaotuanjian.modules.system.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.query.QueryGenerator;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.common.util.PasswordUtil;
import com.woyaotuanjian.common.util.OConvertUtils;
import com.woyaotuanjian.common.util.UserSessionUtil;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import com.woyaotuanjian.modules.system.entity.SysRole;
import com.woyaotuanjian.modules.system.entity.SysUser;
import com.woyaotuanjian.modules.system.entity.SysUserDepart;
import com.woyaotuanjian.modules.system.entity.ext.SysUserExt;
import com.woyaotuanjian.modules.system.model.DepartIdModel;
import com.woyaotuanjian.modules.system.model.SysUserDepartsVO;
import com.woyaotuanjian.modules.system.service.*;
import com.woyaotuanjian.modules.system.vo.SysDepartUsersVO;
import com.woyaotuanjian.modules.system.vo.SysUserProfileVO;
import com.woyaotuanjian.modules.system.vo.SysUserRoleVO;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.entity.BizCompany;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import com.woyaotuanjian.modules.system.entity.SysUserRole;
import com.woyaotuanjian.modules.system.mapper.SysUserDepartMapper;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 用户表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("/sys/user")
public class SysUserController {

	@Autowired
	private ISysUserService sysUserService;

	@Autowired
	private ISysUserRoleService sysUserRoleService;

	@Autowired
	private ISysUserDepartService sysUserDepartService;

	@Autowired
	private ISysUserRoleService userRoleService;
	@Autowired
	private SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysUserProfessionalService sysUserProfessionalService;
    @Autowired
    private UserSessionUtil userSessionUtil;
    @Autowired
    private IBizCompanyService bizCompanyService;


    /**
     * 分页列表查询
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "管理员-分页列表查询")
    @ApiOperation(value="管理员-分页列表查询", notes="管理员-分页列表查询")
    @GetMapping(value = "/list")
    @RequiresRoles(logical = Logical.OR,value = {RoleConstant.ADMIN,RoleConstant.B1SUPER,RoleConstant.B1})
    public Result queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                HttpServletRequest req) {
        Result result = new Result();
        Map<String,Object> param=YdUtil.sqlMap(req.getParameterMap());
        LoginUser loginUser= SysUserUtil.getCurrentUser();
        if(loginUser.getRoleCode().equals(RoleConstant.ADMIN)){
            //不限
        }else if(loginUser.getRoleCode().startsWith(RoleConstant.B1)){
            param.put("comId",loginUser.getComId());
        }else if(loginUser.getRoleCode().startsWith(RoleConstant.B2)){
            param.put("comId",-1);
        }
        IPage<SysUserExt> pageList = sysUserService.getExtPageList(new Page(pageNo, pageSize),param );
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

	@RequestMapping(value = "/add", method = RequestMethod.POST)
    @RequiresRoles(logical = Logical.OR,value = {RoleConstant.ADMIN,RoleConstant.B1SUPER,RoleConstant.B1})
    public Result<SysUser> add(@RequestBody JSONObject jsonObject) {
		Result<SysUser> result = new Result<SysUser>();
        LoginUser loginUser= SysUserUtil.getCurrentUser();
		try {
			SysUser user = JSON.parseObject(jsonObject.toJSONString(), SysUser.class);
			
			// 检查登录账号是否已存在
			if (OConvertUtils.isNotEmpty(user.getUsername())) {
				SysUser existingUser = sysUserService.getOne(new LambdaQueryWrapper<SysUser>()
						.eq(SysUser::getUsername, user.getUsername()));
				if (existingUser != null) {
					result.error500("登录账号已存在，请重新填写！");
					return result;
				}
			}
			
            //设置默认角色
            if(YdUtil.empty(user.getRoleCode())){
                user.setRoleCode("biz");
            }
            String roleIds="";
            List<SysRole> roleList=sysRoleService
                    .list(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode,user.getRoleCode()));
            if(!CollectionUtils.isEmpty(roleList)){
                roleIds=roleList.get(0).getId()+"";
            }

            //设置公司
            if(YdUtil.empty(user.getComId())){
                //admin只能是0
                if(user.getRoleCode().startsWith(RoleConstant.ADMIN)) {
                    user.setComId(0L);
                }else if(user.getRoleCode().startsWith(RoleConstant.B1)) {
                    if(!loginUser.getRoleCode().equals(RoleConstant.ADMIN)){
                        //被修改人是biz，修改人不是admin，也就是本公司修改本公司
                        user.setComId(loginUser.getComId());
                    }
                }else if(user.getRoleCode().startsWith(RoleConstant.B2)){
                    //shop不应该有comid
                }
            }

            // 校验B1账号数量是否超过限制
            if(user.getRoleCode().startsWith(RoleConstant.B1) && user.getComId() != null){
                BizCompany company = bizCompanyService.getById(user.getComId());
                if(company != null && company.getMaxAccountNum() != null && company.getMaxAccountNum() > 0){
                    int count = sysUserService.countB1UsersByComId(user.getComId());
                    if(count >= company.getMaxAccountNum()){
                        result.error500("账号数已达上限（当前：" + count + "个，上限：" + company.getMaxAccountNum() + "个），无法新增，请联系管理员升级套餐");
                        return result;
                    }
                }
            }
            //设置默认密码
            if(YdUtil.empty(user.getPassword())){
                user.setPassword(user.getUsername());
            }
			user.setCreateTime(new Date());//设置创建时间
			user.setUpdateTime(new Date());//设置创建时间
			String salt = OConvertUtils.randomGen(8);
			user.setSalt(salt);
			String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), salt);
			user.setPassword(passwordEncode);
            //默认有效
			user.setStatus(1);

			sysUserService.addUserWithRole(user, roleIds);
			result.success("添加成功！");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.error500("操作失败");
		}
		return result;
	}

	@RequestMapping(value = "/edit", method = RequestMethod.PUT)
	public Result<SysUser> edit(@RequestBody JSONObject jsonObject) {
		Result<SysUser> result = new Result<SysUser>();
        LoginUser loginUser= SysUserUtil.getCurrentUser();

        try {
			SysUser dbUser = sysUserService.getById(jsonObject.getString("id"));
			if(dbUser==null) {
				result.error500("未找到对应实体");
			}else {
				SysUser user = JSON.parseObject(jsonObject.toJSONString(), SysUser.class);
                //设置默认角色
                if(YdUtil.empty(user.getRoleCode())){
                    user.setRoleCode("biz");
                }
                String roleIds="";
                List<SysRole> roleList=sysRoleService
                        .list(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode,user.getRoleCode()));
                if(!CollectionUtils.isEmpty(roleList)){
                    roleIds=roleList.get(0).getId()+"";
                }

                //设置公司
                if(YdUtil.empty(user.getComId())){
                    //admin只能是0
                    if(user.getRoleCode().startsWith(RoleConstant.ADMIN)) {
                        user.setComId(0L);
                    }else if(user.getRoleCode().startsWith(RoleConstant.B1)) {
                        if(!loginUser.getRoleCode().equals(RoleConstant.ADMIN)){
                            //被修改人是biz，修改人不是admin，也就是本公司修改本公司
                            user.setComId(loginUser.getComId());
                        }
                   }else if(user.getRoleCode().startsWith(RoleConstant.B2)){
                       //shop不应该有comid
                   }
                }
				user.setUpdateTime(new Date());
				user.setPassword(dbUser.getPassword());
				user.setSalt(dbUser.getSalt());
				sysUserService.editUserWithRole(user, roleIds);
				result.success("修改成功!");
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.error500("操作失败");
		}
		return result;
	}

	/**
	 * 删除用户
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.DELETE)
	public Result<SysUser> delete(@RequestParam(name="id",required=true) String id) {
		Result<SysUser> result = new Result<SysUser>();
		// 定义SysUserDepart实体类的数据库查询LambdaQueryWrapper
		LambdaQueryWrapper<SysUserDepart> query = new LambdaQueryWrapper<SysUserDepart>();
		SysUser sysUser = sysUserService.getById(id);
		if(sysUser==null) {
			result.error500("未找到对应实体");
		}else {
			// 当某个用户被删除时,删除其ID下对应的部门数据
			query.eq(SysUserDepart::getUserId, id);
			boolean ok = sysUserService.removeById(id);
			sysUserDepartService.remove(query);
			if(ok) {
				result.success("删除成功!");
			}
		}

		return result;
	}

	/**
	 * 批量删除用户
	 */
	@RequestMapping(value = "/deleteBatch", method = RequestMethod.DELETE)
	public Result<SysUser> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		// 定义SysUserDepart实体类的数据库查询对象LambdaQueryWrapper
		LambdaQueryWrapper<SysUserDepart> query = new LambdaQueryWrapper<SysUserDepart>();
		String[] idArry = ids.split(",");
		Result<SysUser> result = new Result<SysUser>();
		if(ids==null || "".equals(ids.trim())) {
			result.error500("参数不识别！");
		}else {
			this.sysUserService.removeByIds(Arrays.asList(ids.split(",")));
			// 当批量删除时,删除在SysUserDepart中对应的所有部门数据
			for(String id : idArry) {
				query.eq(SysUserDepart::getUserId, id);
				this.sysUserDepartService.remove(query);
			}
			result.success("删除成功!");
		}
		return result;
	}

	/**
	  * 冻结&解冻用户
	 * @param jsonObject
	 * @return
	 */
	@RequestMapping(value = "/frozenBatch", method = RequestMethod.PUT)
	public Result<SysUser> frozenBatch(@RequestBody JSONObject jsonObject) {
		Result<SysUser> result = new Result<SysUser>();
		try {
			String ids = jsonObject.getString("ids");
			String status = jsonObject.getString("status");
			String[] arr = ids.split(",");
			for (String id : arr) {
				if(OConvertUtils.isNotEmpty(id)) {
					this.sysUserService.update(new SysUser().setStatus(Integer.parseInt(status)),
							new UpdateWrapper<SysUser>().lambda().eq(SysUser::getId,id));
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			result.error500("操作失败"+e.getMessage());
		}
		result.success("操作成功!");
		return result;

    }

    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<SysUser> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<SysUser> result = new Result<SysUser>();
        SysUser sysUser = sysUserService.getById(id);
        if (sysUser == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(sysUser);
            result.setSuccess(true);
        }
        return result;
    }

    @RequestMapping(value = "/queryUserRole", method = RequestMethod.GET)
    public Result<List<Integer>> queryUserRole(@RequestParam(name = "userid", required = true) String userid) {
        Result<List<Integer>> result = new Result<>();

        List<SysUserRole> userRole = sysUserRoleService.list(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, userid));
		List<Integer> list = new ArrayList<>(userRole.size());
        if (userRole == null || userRole.size() <= 0) {
            result.error500("未找到用户相关角色信息");
        } else {
            for (SysUserRole sysUserRole : userRole) {
                list.add(sysUserRole.getRoleId());
            }
            result.setSuccess(true);
            result.setResult(list);
        }
        return result;
    }


    /**
     * 校验用户账号是否唯一<br>
     * 可以校验其他 需要检验什么就传什么。。。
     *
     * @param sysUser
     * @return
     */
    @RequestMapping(value = "/checkOnlyUser", method = RequestMethod.GET)
    public Result<Boolean> checkUsername(SysUser sysUser) {
        Result<Boolean> result = new Result<>();
        result.setResult(true);//如果此参数为false则程序发生异常
        Integer id = sysUser.getId();
        log.info("--验证用户信息是否唯一---id:" + id);
        try {
            SysUser oldUser = null;
            if (OConvertUtils.isNotEmpty(id)) {
                oldUser = sysUserService.getById(id);
            } else {
                sysUser.setId(null);
            }
            //通过传入信息查询新的用户信息
            SysUser newUser = sysUserService.getOne(new QueryWrapper<SysUser>(sysUser));
            if (newUser != null) {
                //如果根据传入信息查询到用户了，那么就需要做校验了。
                if (oldUser == null) {
                    //oldUser为空=>新增模式=>只要用户信息存在则返回false
                    result.setSuccess(false);
                    result.setMessage("用户账号已存在");
                    return result;
                } else if (!id.equals(newUser.getId())) {
                    //否则=>编辑模式=>判断两者ID是否一致-
                    result.setSuccess(false);
                    result.setMessage("用户账号已存在");
                    return result;
                }
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage(e.getMessage());
            return result;
        }
        result.setSuccess(true);
        return result;
    }

    /**
     * 系统管理修改用户密码(管理员修改任何人的)
     */
    @RequestMapping(value = "/changPassword", method = RequestMethod.PUT)
    public Result changPassword(@RequestBody JSONObject jsonObject) {
        Result result = new Result();
        String password = jsonObject.getString("password");
        String username = jsonObject.getString("username");
        SysUser dbUser = sysUserService.getOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, username));
        if (dbUser == null) {
            result.error500("未找到对应实体");
        } else {
            String salt = OConvertUtils.randomGen(8);
            String passwordEncode = PasswordUtil.encrypt(dbUser.getUsername(), password, salt);
            dbUser.setSalt(salt);
            dbUser.setPassword(passwordEncode);
            sysUserService.updateById(dbUser);
            
            // 修改密码后踢出该用户的所有登录会话
            int kickedCount = sysUserService.kickOutUser(username);
            
            dbUser.setPassword(null);
            dbUser.setSalt(null);
            result.setResult(dbUser);
            result.success("密码修改完成！已踢出 " + kickedCount + " 个登录会话，用户需要重新登录。");
        }
        return result;
    }

    /**
     * 修改自己电话邮箱性别等
     */
    @GetMapping(value = "/currentInfo")
    public Result<SysUser> currentInfo() {
        Result<SysUser> result = new Result<SysUser>();
        LoginUser sessionSysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysUser dbSysUser = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, sessionSysUser.getUsername()));
        if (dbSysUser == null) {
            result.error500("未找到对应实体");
        } else {
            dbSysUser.setPassword(null);
            dbSysUser.setSalt(null);
            result.setResult(dbSysUser);
            result.success("查询完成！");
        }
        return result;
    }
    /**
     * 修改自己电话邮箱性别等
     */
    @RequestMapping(value = "/changeInfo", method = RequestMethod.PUT)
    public Result<SysUser> changInfo(@RequestBody SysUser sysUser) {
        Result<SysUser> result = new Result<SysUser>();
        LoginUser sessionSysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysUser dbSysUser = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, sessionSysUser.getUsername()));
        if (dbSysUser == null) {
            result.error500("未找到对应实体");
        } else {
            if(!YdUtil.empty(sysUser.getPhone())){
                dbSysUser.setPhone(sysUser.getPhone());
            }
            if(!YdUtil.empty(sysUser.getEmail())){
                dbSysUser.setEmail(sysUser.getEmail());
            }
            if(!YdUtil.empty(sysUser.getRealname())){
                dbSysUser.setRealname(sysUser.getRealname());
            }
            if(!YdUtil.empty(sysUser.getSex())){
                dbSysUser.setSex(sysUser.getSex());
            }
            if(!YdUtil.empty(sysUser.getBirthday())){
                dbSysUser.setBirthday(sysUser.getBirthday());
            }
            this.sysUserService.updateById(dbSysUser);
            result.setResult(dbSysUser);
            result.success("修改完成！");
        }
        return result;
    }

    /**
     * 查询指定用户和部门关联的数据
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/userDepartList", method = RequestMethod.GET)
    public Result<List<DepartIdModel>> getUserDepartsList(@RequestParam(name = "userId", required = true) String userId) {
        Result<List<DepartIdModel>> result = new Result<>();
        try {
            List<DepartIdModel> depIdModelList = this.sysUserDepartService.queryDepartIdsOfUser(userId);
            if (depIdModelList != null && depIdModelList.size() > 0) {
                result.setSuccess(true);
                result.setMessage("查找成功");
                result.setResult(depIdModelList);
            } else {
                result.setSuccess(false);
                result.setMessage("查找失败");
            }
            return result;
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
            result.setSuccess(false);
            result.setMessage("查找过程中出现了异常: " + e.getMessage());
            return result;
        }

    }

    /**
     * 给指定用户添加对应的部门
     *
     * @param sysUserDepartsVO
     * @return
     */
    @RequestMapping(value = "/addUDepartIds", method = RequestMethod.POST)
    public Result<String> addSysUseWithrDepart(@RequestBody SysUserDepartsVO sysUserDepartsVO) {
        boolean ok = this.sysUserDepartService.addSysUseWithrDepart(sysUserDepartsVO);
        Result<String> result = new Result<String>();
        try {
            if (ok) {
                result.setMessage("添加成功!");
                result.setSuccess(true);
            } else {
                throw new Exception("添加失败!");
            }
            return result;
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
            result.setSuccess(true);
            result.setMessage("添加数据的过程中出现市场了: " + e.getMessage());
            return result;
        }

    }

    /**
     * 根据用户id编辑对应的部门信息
     *
     * @param sysUserDepartsVO
     * @return
     */
    @RequestMapping(value = "/editUDepartIds", method = RequestMethod.PUT)
    public Result editSysUserWithDepart(@RequestBody SysUserDepartsVO sysUserDepartsVO) {
    	if(sysUserDepartsVO==null||sysUserDepartsVO.getUserId()==null){
    		return Result.ok();
		}
    	//删除旧的部门
		sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().eq(SysUserDepart::getUserId,sysUserDepartsVO.getUserId()));
    	//如果有新部门，添加新的部门
        if(sysUserDepartsVO.getDepartIdList()!=null&&sysUserDepartsVO.getDepartIdList().size()>0){
			sysUserDepartsVO.getDepartIdList().stream().forEach(departId->{
				sysUserDepartMapper.insert(new SysUserDepart(null,sysUserDepartsVO.getUserId(),departId));
			});
		}
        return Result.ok();
    }

    /**
     * 生成在添加用户情况下没有主键的问题,返回给前端,根据该id绑定部门数据
     *
     * @return
     */
    @RequestMapping(value = "/generateUserId", method = RequestMethod.GET)
    public Result<Integer> generateUserId() {
        Result<Integer> result = new Result<>();
        result.setSuccess(true);
        result.setResult(100000);
        return result;
    }

    /**
     * 根据部门id查询用户信息
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/queryUserByDepId", method = RequestMethod.GET)
    public Result<List<SysUser>> queryUserByDepId(@RequestParam(name = "id", required = true) String id) {
        Result<List<SysUser>> result = new Result<>();
        List<SysUser> userList = sysUserDepartService.queryUserByDepId(id);
        try {
            result.setSuccess(true);
            result.setResult(userList);
            return result;
        } catch (Exception e) {
        	log.error(e.getMessage(), e);
            result.setSuccess(false);
            return result;
        }
    }

    /**
     * 查询所有用户所对应的角色信息
     *
     * @return
     */
    @RequestMapping(value = "/queryUserRoleMap", method = RequestMethod.GET)
    public Result<Map> queryUserRole() {
        Result result = new Result<>();
        Map<Integer, String> map = userRoleService.queryUserRole();
        result.setResult(map);
        result.setSuccess(true);
        return result;
    }


    /**
	 * @功能：根据id 批量查询
	 * @param userIds
	 * @return
	 */
	@RequestMapping(value = "/queryByIds", method = RequestMethod.GET)
	public Result<Collection<SysUser>> queryByIds(@RequestParam String userIds) {
		Result<Collection<SysUser>> result = new Result<>();
		String[] userId = userIds.split(",");
		Collection<String> idList = Arrays.asList(userId);
		Collection<SysUser> userRole = sysUserService.listByIds(idList);
		result.setSuccess(true);
		result.setResult(userRole);
		return result;
	}

	/**
	 * 首页密码修改
	 */
	@RequestMapping(value = "/updatePassword", method = RequestMethod.PUT)
	public Result<SysUser> updatePassword(@RequestBody JSONObject json) {
		Result<SysUser> result = new Result<SysUser>();
		String username = json.getString("username");
		String oldpassword = json.getString("oldpassword");
		SysUser user = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username));
		if(user==null) {
			result.error500("未找到用户!");
			return result;
		}
		String passwordEncode = PasswordUtil.encrypt(username, oldpassword, user.getSalt());
		if(!user.getPassword().equals(passwordEncode)) {
			result.error500("旧密码输入错误!");
			return result;
		}

		String password = json.getString("password");
		String confirmpassword = json.getString("confirmpassword");
		if(OConvertUtils.isEmpty(password)) {
			result.error500("新密码不存在!");
			return result;
		}

		if(!password.equals(confirmpassword)) {
			result.error500("两次输入密码不一致!");
			return result;
		}
		String newpassword = PasswordUtil.encrypt(username, password, user.getSalt());
		this.sysUserService.update(new SysUser().setPassword(newpassword), new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, user.getId()));
		
		// 修改密码后踢出该用户的所有登录会话
		int kickedCount = sysUserService.kickOutUser(username);
		
		result.success("密码修改完成！已踢出 " + kickedCount + " 个登录会话，请重新登录。");
		return result;
	}

    @RequestMapping(value = "/userRoleList", method = RequestMethod.GET)
    public Result<IPage<SysUser>> userRoleList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                               @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<IPage<SysUser>>();
        Page<SysUser> page = new Page<SysUser>(pageNo, pageSize);
        String roleId = req.getParameter("roleId");
        String username = req.getParameter("username");
        IPage<SysUser> pageList = sysUserService.getUserByRoleId(page,roleId,username);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 给指定角色添加用户
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/addSysUserRole", method = RequestMethod.POST)
    public Result<String> addSysUserRole(@RequestBody SysUserRoleVO sysUserRoleVO) {
        Result<String> result = new Result<String>();
        try {
			Integer sysRoleId = sysUserRoleVO.getRoleId();
            for(Integer sysUserId:sysUserRoleVO.getUserIdList()) {
                SysUserRole sysUserRole = new SysUserRole(sysUserId,sysRoleId);
                QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<SysUserRole>();
                queryWrapper.eq("role_id", sysRoleId).eq("user_id",sysUserId);
                SysUserRole one = sysUserRoleService.getOne(queryWrapper);
                if(one==null){
                    sysUserRoleService.save(sysUserRole);
                }

            }

            result.setMessage("添加成功!");
            result.setSuccess(true);
            return result;
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.setSuccess(false);
            result.setMessage("出错了: " + e.getMessage());
            return result;
        }
    }
    /**
     *   删除指定角色的用户关系
     * @param
     * @return
     */
    @RequestMapping(value = "/deleteUserRole", method = RequestMethod.DELETE)
    public Result<SysUserRole> deleteUserRole(@RequestParam(name="roleId") String roleId,
                                                    @RequestParam(name="userId",required=true) String userId
    ) {
        Result<SysUserRole> result = new Result<SysUserRole>();
        try {
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<SysUserRole>();
            queryWrapper.eq("role_id", roleId).eq("user_id",userId);
            sysUserRoleService.remove(queryWrapper);
            result.success("删除成功!");
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.error500("删除失败！");
        }
        return result;
    }

    /**
     * 批量删除指定角色的用户关系
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/deleteUserRoleBatch", method = RequestMethod.DELETE)
    public Result<SysUserRole> deleteUserRoleBatch(
            @RequestParam(name="roleId") String roleId,
            @RequestParam(name="userIds",required=true) String userIds) {
        Result<SysUserRole> result = new Result<SysUserRole>();
        try {
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<SysUserRole>();
            queryWrapper.eq("role_id", roleId).in("user_id",Arrays.asList(userIds.split(",")));
            sysUserRoleService.remove(queryWrapper);
            result.success("删除成功!");
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.error500("删除失败！");
        }
        return result;
    }

    /**
     * 部门用户列表
     */
    @RequestMapping(value = "/departUserList", method = RequestMethod.GET)
    public Result<IPage<SysUser>> departUserList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<IPage<SysUser>>();
        Page<SysUser> page = new Page<SysUser>(pageNo, pageSize);
        String depId = req.getParameter("depId");
        String username = req.getParameter("username");
        IPage<SysUser> pageList = sysUserService.getUserByDepId(page,depId,username);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 给指定部门添加对应的用户
     */
    @RequestMapping(value = "/editSysDepartWithUser", method = RequestMethod.POST)
    public Result<String> editSysDepartWithUser(@RequestBody SysDepartUsersVO sysDepartUsersVO) {
        Result<String> result = new Result<String>();
        try {
			Integer sysDepId = sysDepartUsersVO.getDepId();
            for(Integer sysUserId:sysDepartUsersVO.getUserIdList()) {
                SysUserDepart sysUserDepart = new SysUserDepart(null,sysUserId,sysDepId);
                QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<SysUserDepart>();
                queryWrapper.eq("dep_id", sysDepId).eq("user_id",sysUserId);
                SysUserDepart one = sysUserDepartService.getOne(queryWrapper);
                if(one==null){
                    sysUserDepartService.save(sysUserDepart);
                }
            }

            result.setMessage("添加成功!");
            result.setSuccess(true);
            return result;
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.setSuccess(false);
            result.setMessage("出错了: " + e.getMessage());
            return result;
        }
    }

    /**
     *   删除指定机构的用户关系
     */
    @RequestMapping(value = "/deleteUserInDepart", method = RequestMethod.DELETE)
    public Result<SysUserDepart> deleteUserInDepart(@RequestParam(name="depId") String depId,
                                                    @RequestParam(name="userId",required=true) String userId
    ) {
        Result<SysUserDepart> result = new Result<SysUserDepart>();
        try {
            QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<SysUserDepart>();
            queryWrapper.eq("dep_id", depId).eq("user_id",userId);
            sysUserDepartService.remove(queryWrapper);
            result.success("删除成功!");
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.error500("删除失败！");
        }
        return result;
    }

    /**
     * 批量删除指定机构的用户关系
     */
    @RequestMapping(value = "/deleteUserInDepartBatch", method = RequestMethod.DELETE)
    public Result<SysUserDepart> deleteUserInDepartBatch(
            @RequestParam(name="depId") String depId,
            @RequestParam(name="userIds",required=true) String userIds) {
        Result<SysUserDepart> result = new Result<SysUserDepart>();
        try {
            QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<SysUserDepart>();
            queryWrapper.eq("dep_id", depId).in("user_id",Arrays.asList(userIds.split(",")));
            sysUserDepartService.remove(queryWrapper);
            result.success("删除成功!");
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            result.error500("删除失败！");
        }
        return result;
    }

    /**
     * 获取用户资料
     */
    @RequestMapping(value = "/profile/{username}", method = RequestMethod.GET)
    public Result<SysUserProfileVO> getUserProfile(@PathVariable String username) {
        Result<SysUserProfileVO> result = new Result<>();
        try {
            SysUser user = sysUserService.getUserByName(username);
            if(user != null) {
                SysUserProfileVO profile = sysUserProfessionalService.getUserProfile(user.getId());
                result.setSuccess(true);
                result.setResult(profile);
            } else {
                result.error500("未找到用户信息");
            }
        } catch (Exception e) {
            log.error("获取用户资料失败", e);
            result.error500("获取用户资料失败");
        }
        return result;
    }

    /**
     * 更新用户资料
     */
    @RequestMapping(value = "/profile", method = RequestMethod.PUT)
    public Result<SysUserProfileVO> updateProfile(@RequestBody SysUserProfileVO profileVO) {
        Result<SysUserProfileVO> result = new Result<>();
        try {
            // 安全检查：确保只能修改自己的资料
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser == null || !sysUser.getId().equals(profileVO.getId())) {
                result.error500("无权修改他人资料！");
                return result;
            }

            // 确保有userId
            if (profileVO.getId() == null) {
                result.error500("用户ID不能为空！");
                return result;
            }

            sysUserProfessionalService.updateUserProfile(profileVO);
            result.success("更新成功！");
        } catch (Exception e) {
            log.error("更新用户资料失败", e);
            result.error500("更新用户资料失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 踢出用户登录会话
     */
    @RequestMapping(value = "/kickOut", method = RequestMethod.POST)
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result<String> kickOutUser(@RequestParam(name = "username", required = true) String username) {
        Result<String> result = new Result<>();
        try {
            // 检查用户是否存在
            SysUser user = sysUserService.getUserByName(username);
            if (user == null) {
                result.error500("用户不存在！");
                return result;
            }

            // 踢出用户的所有登录会话
            int kickedCount = userSessionUtil.kickOutUser(username);
            
            result.success("成功踢出用户 " + username + " 的 " + kickedCount + " 个登录会话");
        } catch (Exception e) {
            log.error("踢出用户登录会话失败", e);
            result.error500("踢出用户登录会话失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户活跃会话数量
     */
    @RequestMapping(value = "/getActiveSessionCount", method = RequestMethod.GET)
    @RequiresRoles(logical = Logical.OR, value = {RoleConstant.ADMIN, RoleConstant.B1SUPER})
    public Result<Integer> getActiveSessionCount(@RequestParam(name = "username", required = true) String username) {
        Result<Integer> result = new Result<>();
        try {
            // 检查用户是否存在
            SysUser user = sysUserService.getUserByName(username);
            if (user == null) {
                result.error500("用户不存在！");
                return result;
            }

            // 获取用户活跃会话数量
            int sessionCount = userSessionUtil.getActiveSessionCount(username);
            result.setResult(sessionCount);
            result.success("获取成功");
        } catch (Exception e) {
            log.error("获取用户活跃会话数量失败", e);
            result.error500("获取用户活跃会话数量失败: " + e.getMessage());
        }
        return result;
    }

}
