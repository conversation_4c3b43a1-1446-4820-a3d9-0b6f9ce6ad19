package com.woyaotuanjian.modules.system.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.system.vo.SysUserCacheInfo;
import com.woyaotuanjian.modules.system.entity.SysUser;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-20
 */
public interface ISysUserService extends IService<SysUser> {

	Page getExtPageList(Page page, Map<String, Object> map);
	
	SysUser getUserByName(String username);
	
	/**
	 * 添加用户和用户角色关系
	 * @param user
	 * @param roles
	 */
	void addUserWithRole(SysUser user,String roles);

	/**
	 * 给店主注册账号
	 * @param loginName
	 * @param password
	 * @param nickName
	 */
	Result registerShopUser(String loginName, String password,String nickName);
	/**
	 * 给店主修改密码
	 * @param loginName
	 * @param password
	 */
	Result updateShopUserPassword(String loginName, String password);
	/**
	 * 修改用户和用户角色关系
	 * @param user
	 * @param roles
	 */
	void editUserWithRole(SysUser user,String roles);

	/**
	 * 获取用户的授权角色
	 * @param username
	 * @return
	 */
	List<String> getRole(String username);
	
	/**
	  * 查询用户信息包括 部门信息
	 * @param username
	 * @return
	 */
	SysUserCacheInfo getCacheUser(String username);

	/**
	 * 根据部门Id查询
	 * @param
	 * @return
	 */
	IPage<SysUser> getUserByDepId(Page<SysUser> page, String departId, String username);

	/**
	 * 根据角色Id查询
	 * @param
	 * @return
	 */
	IPage<SysUser> getUserByRoleId(Page<SysUser> page,String roleId, String username);

	/**
	 * 通过用户名获取用户角色集合
	 *
	 * @param username 用户名
	 * @return 角色集合
	 */
	Set<String> getUserRolesSet(String username);

	/**
	 * 通过用户名获取用户权限集合
	 *
	 * @param username 用户名
	 * @return 权限集合
	 */
	Set<String> getUserPermissionsSet(String username);
	
	/**
	 * 根据用户名设置部门ID
	 * @param username
	 * @param orgCode
	 */
	void updateUserDepart(String username,String orgCode);

	/**
	 * 踢出指定用户的所有登录会话
	 * @param username 用户名
	 * @return 踢出的会话数量
	 */
        int kickOutUser(String username);

        /**
         * 统计指定公司下的B1账号数量
         * @param comId 公司ID
         * @return 数量
         */
        int countB1UsersByComId(Long comId);
}
