package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import com.woyaotuanjian.common.aspect.annotation.Dict;

/**
 * @Description: 公司列表
 */
@Data
@TableName("biz_company")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="biz_company对象", description="公司列表")
public class BizCompany {
    
	/**id*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
	private java.lang.Long id;
	/**公司*/
	@Excel(name = "公司", width = 15)
    @ApiModelProperty(value = "公司")
	private java.lang.String companyName;
	/**介绍*/
	@Excel(name = "介绍", width = 15)
    @ApiModelProperty(value = "介绍")
	private java.lang.String companyDesc;
	/**店铺-包团链接id*/
	@Excel(name = "店铺", width = 15)
	@ApiModelProperty(value = "店铺")
	private java.lang.Long companyShopId;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy/MM/dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
	private java.util.Date createTime;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy/MM/dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
	private java.util.Date updateTime;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
	private java.lang.Integer status;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
	private java.lang.Integer version;
	/**图片*/
	@Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
	private java.lang.String imgUrl;
	/**管理员id*/
	@Excel(name = "管理员id", width = 15)
    @ApiModelProperty(value = "管理员id")
	private java.lang.Integer sysUserId;

	@ApiModelProperty(value = "公司简码")
	private java.lang.String companyCode;

	@ApiModelProperty(value = "导出word样式")
	private java.lang.String wordStyle;

	@ApiModelProperty(value = "导出word背景图")
	private String wordBgConfig;

	@ApiModelProperty(value = "内容控制标志配置")
	private String contentFlagConfig;

	@ApiModelProperty(value = "导出ppt样式")
	private java.lang.String pptStyle;

	@ApiModelProperty(value = "配置中心")
	private java.lang.String config;

	@ApiModelProperty(value = "默认日程配置")
	private String defaultScheduleConfig;

	@ApiModelProperty(value = "行程表配置")
	private String scheduleConfig;
	
        @ApiModelProperty(value = "会员等级")
        @Dict(dicCode = "biz_member_grade")
        private Integer memberGrade;

        @ApiModelProperty(value = "最大账号数")
        private Integer maxAccountNum;
	
	@ApiModelProperty(value = "到期时间")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	private java.util.Date expireTime;

	@Excel(name = "开票名称", width = 15)
	@ApiModelProperty(value = "开票名称")
	private java.lang.String invoiceName;

	@Excel(name = "联系人", width = 15)
	@ApiModelProperty(value = "联系人")
	private java.lang.String contactPerson;
	
	@Excel(name = "联系电话", width = 15)
	@ApiModelProperty(value = "联系电话")
	private java.lang.String contactPhone;
	
        @Excel(name = "微信", width = 15)
        @ApiModelProperty(value = "微信")
        private java.lang.String wechat;

        @ApiModelProperty(value = "附件")
        private java.lang.String attach;

        @ApiModelProperty(value = "管理备注")
        private String adminRemark;

}
