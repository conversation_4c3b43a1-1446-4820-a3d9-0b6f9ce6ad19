import { UserLayout, TabLayout, RouteView, BlankLayout, PageView } from '@/components/layouts';

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [
  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/workplace',
    children: [

    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
];

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult')
      }
    ]
  },
  {
    path: '/',
    name: 'index',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: [
      {
        path: '/biz/bizScenic/detail',
        name: 'biz-bizScenic-detail' ,
        component: () => import('@/views/biz/BizScenic/modules/BizScenicDetail'),
        meta: { title: '景区详情', icon: 'dashboard' }
      },
      {
        path: '/biz/bizScenic/edit',
        name: 'biz-bizScenic-edit' ,
        component: () => import('@/views/biz/BizScenic/modules/BizScenicEdit'),
        meta: { title: '景区编辑', icon: 'dashboard' }
      },
      {
        path: '/biz/bizHotel/edit',
        name: 'biz-bizHotel-edit' ,
        component: () => import('@/views/biz/BizHotel/modules/BizHotelEdit'),
        meta: { title: '酒店编辑', icon: 'dashboard' }
      },
      {
        path: '/biz/bizRestaurant/edit',
        name: 'biz-bizRestaurant-edit' ,
        component: () => import('@/views/biz/BizRestaurant/modules/BizRestaurantEdit'),
        meta: { title: '餐厅编辑', icon: 'dashboard' }
      },   
      {
        path: '/biz/bizTraffic/edit',
        name: 'biz-bizTraffic-edit' ,
        component: () => import('@/views/biz/BizTraffic/modules/BizTrafficEdit'),
        meta: { title: '交通编辑', icon: 'dashboard' }
      },    
      {
        path: '/biz/bizTrip/edit',
        name: 'biz-bizTrip-edit' ,
        component: () => import('@/views/biz/BizTrip/modules/BizTripEdit'),
        meta: { title: '行程编辑', icon: 'dashboard' }
      },
      {
        path: '/biz/bizTrip/aiContent',
        name: 'biz-bizTrip-aiContent',
        component: () => import('@/views/biz/BizTrip/modules/BizTripAiContent'),
        meta: { title: '小程序H5编辑', icon: 'dashboard' }
      },
      {
        path: '/biz/bizTheme/edit',
        name: 'biz-bizTheme-edit' ,
        component: () => import('@/views/biz/BizTheme/modules/BizThemeEdit'),
        meta: { title: '主题编辑', icon: 'dashboard' }
      },
      {
        path: '/biz/quick-quote',
        name: 'biz-quick-quote',
        component: () => import('@/views/biz/QuickQuote/QuickQuote'),
        meta: { title: '快捷报价', icon: 'calculator' }
      },
      {
        path: '/biz/quick-quote/list',
        name: 'biz-quick-quote-list',
        component: () => import('@/views/biz/QuickQuote/QuickQuoteList'),
        meta: { title: '快捷报价管理', icon: 'table' }
      },
    ]
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  }
];
