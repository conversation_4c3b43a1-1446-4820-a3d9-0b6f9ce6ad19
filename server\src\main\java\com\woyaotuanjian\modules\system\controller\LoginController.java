package com.woyaotuanjian.modules.system.controller;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.constant.CommonConstant;
import com.woyaotuanjian.common.system.api.ISysBaseAPI;
import com.woyaotuanjian.common.system.util.JwtUtil;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.common.util.PasswordUtil;
import com.woyaotuanjian.common.util.RedisUtil;
import com.woyaotuanjian.common.util.UserSessionUtil;
import com.woyaotuanjian.modules.shiro.vo.DefContants;
import com.woyaotuanjian.modules.system.model.SysLoginModel;
import com.woyaotuanjian.modules.system.service.ISysUserService;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.entity.BizCompany;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import com.woyaotuanjian.modules.system.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 */
@RestController
@RequestMapping("/sys")
@Api(tags = "用户登录")
@Slf4j
public class LoginController {
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IBizCompanyService bizCompanyService;
    @Autowired
    private UserSessionUtil userSessionUtil;

    private static final String LOGIN_FAIL_COUNT = "login_fail_count_";
    private static final String CAPTCHA_VERIFIED = "captcha_verified_";
    private static final int MAX_FAIL_COUNT = 3; // 最大失败次数，超过需要验证码

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ApiOperation("登录接口")
    public Result<JSONObject> login(@RequestBody SysLoginModel sysLoginModel, HttpServletRequest request) {
        Result<JSONObject> result = new Result<JSONObject>();
        String username = sysLoginModel.getUsername();
        String password = sysLoginModel.getPassword();
        
        String clientId = getClientIdentifier(request);
        Object countObj = redisUtil.get(LOGIN_FAIL_COUNT + clientId);
        int failCount = 0;
        if (countObj != null) {
            try {
                failCount = (Integer) countObj;
            } catch (ClassCastException e) {
                // 如果类型转换失败，尝试其他方式解析
                if (countObj instanceof Number) {
                    failCount = ((Number) countObj).intValue();
                } else if (countObj instanceof String) {
                    failCount = Integer.parseInt(countObj.toString());
                } else {
                    failCount = 0; // 默认值
                }
            }
        }

        if (failCount >= MAX_FAIL_COUNT) {
            // 需要验证码验证
            String captchaId = sysLoginModel.getCaptchaId();
            
            if (captchaId == null) {
                result.error500("请完成滑块验证");
                return result;
            }
            
            // 检查验证码是否已验证通过
            Object verifiedObj = redisUtil.get(CAPTCHA_VERIFIED + captchaId);
            if (verifiedObj == null || !Boolean.TRUE.equals(verifiedObj)) {
                result.error500("请先完成滑块验证");
                return result;
            }
            
            // 验证码验证通过，删除验证标记
            redisUtil.del(CAPTCHA_VERIFIED + captchaId);
        }
        
        SysUser sysUser = sysUserService.getUserByName(username);
        if (sysUser == null) {
            // 增加登录失败次数
            incrementFailCount(clientId);
            
            result.error500("该用户不存在");
            sysBaseAPI.addLog("登录失败，用户名:" + username + "不存在！", CommonConstant.LOG_TYPE_1, null);
            return result;
        } else {
            //密码验证
            String userpassword = PasswordUtil.encrypt(username, password, sysUser.getSalt());
            String syspassword = sysUser.getPassword();
            if (!syspassword.equals(userpassword)) {
                // 增加登录失败次数
                incrementFailCount(clientId);
                
                result.error500("用户名或密码错误");
                return result;
            }

            if (sysUser.getStatus()==null||!sysUser.getStatus().equals(1)) {
                // 增加登录失败次数
                incrementFailCount(clientId);

                result.error500("登录失败，用户已被锁定，请联系管理员");
                return result;
            }

            // 检查公司是否启用单点登录限制
            boolean singleLogin = false;
            if (sysUser.getComId() != null) {
                BizCompany company = bizCompanyService.getById(sysUser.getComId());
                if (company != null && company.getConfig() != null) {
                    try {
                        JSONObject cfg = JSONObject.parseObject(company.getConfig());
                        singleLogin = cfg.getBooleanValue("singleLogin");
                    } catch (Exception e) {
                        log.error("解析公司配置失败", e);
                    }
                }
            }
            if (singleLogin) {
                userSessionUtil.kickOutUser(username);
            }
            
            // 登录成功，重置失败次数
            redisUtil.del(LOGIN_FAIL_COUNT + clientId);
            
            //生成token
            String token = JwtUtil.sign(username, syspassword);
            redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
            //设置超时时间
            redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME / 1000);
            sysUser.setPassword(null);
            sysUser.setSalt(null);

            //获取用户部门信息
            JSONObject obj = new JSONObject();
            obj.put("departs", null);
            obj.put("multi_depart", 0);
            obj.put("token", token);
            obj.put("userInfo", sysUser);
            result.setResult(obj);
            result.success("登录成功");
            sysBaseAPI.addLog("用户名: " + username + ",登录成功！", CommonConstant.LOG_TYPE_1, null);
        }
        return result;
    }

    /**
     * 退出登录
     * @return
     */
    @RequestMapping(value = "/logout")
    public Result<Object> logout(HttpServletRequest request, HttpServletResponse response) {
        //用户退出逻辑
        Subject subject = SecurityUtils.getSubject();
        LoginUser sysUser = (LoginUser) subject.getPrincipal();
        sysBaseAPI.addLog("用户名: " + sysUser.getUsername() + ",退出成功！", CommonConstant.LOG_TYPE_1, null);
        log.info(" 用户名:  " + sysUser.getUsername() + ",退出成功！ ");
        subject.logout();

        String token = request.getHeader(DefContants.X_ACCESS_TOKEN);
        //清空用户Token缓存
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
        //清空用户权限缓存：权限Perms和角色集合
        redisUtil.del(CommonConstant.LOGIN_USER_CACHERULES_ROLE + sysUser.getUsername());
        redisUtil.del(CommonConstant.LOGIN_USER_CACHERULES_PERMISSION + sysUser.getUsername());
        return Result.ok("退出登录成功！");
    }


    /**
     * 登陆成功选择用户当前部门
     *
     * @param user
     * @return
     */
    @RequestMapping(value = "/selectDepart", method = RequestMethod.PUT)
    public Result<?> selectDepart(@RequestBody SysUser user) {
        String username = user.getUsername();
        String orgCode = user.getOrgCode();
        this.sysUserService.updateUserDepart(username, orgCode);
        return Result.ok();
    }
    
    /**
     * 增加登录失败次数
     */
    private void incrementFailCount(String clientId) {
        Object countObj = redisUtil.get(LOGIN_FAIL_COUNT + clientId);
        int count = 0;
        if (countObj != null) {
            try {
                count = (Integer) countObj;
            } catch (ClassCastException e) {
                // 如果类型转换失败，尝试其他方式解析
                if (countObj instanceof Number) {
                    count = ((Number) countObj).intValue();
                } else if (countObj instanceof String) {
                    count = Integer.parseInt(countObj.toString());
                } else {
                    count = 0; // 默认值
                }
            }
        }
        count++;
        
        // 设置过期时间为24小时
        redisUtil.set(LOGIN_FAIL_COUNT + clientId, count, 24 * 60 * 60);
    }
    
    /**
     * 获取更安全的客户端标识
     * 结合IP、User-Agent、Accept-Language等多个因素
     */
    private String getClientIdentifier(HttpServletRequest request) {
        StringBuilder identifier = new StringBuilder();
        
        // 获取真实IP（防止简单的header伪造）
        String realIp = getRealClientIp(request);
        identifier.append(realIp);
        
        // 添加User-Agent指纹
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && userAgent.length() > 10) {
            identifier.append("_").append(userAgent.hashCode());
        }
        
        // 添加Accept-Language指纹
        String acceptLang = request.getHeader("Accept-Language");
        if (acceptLang != null) {
            identifier.append("_").append(acceptLang.hashCode());
        }
        
        return identifier.toString();
    }
    
    /**
     * 更严格的IP获取方法
     */
    private String getRealClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        // 验证X-Forwarded-For的合法性
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            // 取第一个IP（最原始的客户端IP）
            if (ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }
            // 验证IP格式
            if (isValidIp(ip)) {
                return ip;
            }
        }
        
        // 依次尝试其他header
        String[] headers = {
            "Proxy-Client-IP", 
            "WL-Proxy-Client-IP", 
            "HTTP_CLIENT_IP", 
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : headers) {
            ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip) && isValidIp(ip)) {
                return ip;
            }
        }
        
        // 最后使用直连IP
        return request.getRemoteAddr();
    }
    
    /**
     * 验证IP地址格式
     */
    private boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) return false;
        
        // 简单的IP格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) return false;
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) return false;
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
