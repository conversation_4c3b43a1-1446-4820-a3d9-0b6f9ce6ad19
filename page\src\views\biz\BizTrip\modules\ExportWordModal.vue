<template>
  <div>
    <a-modal title="导出文件" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="onExportFile"
      @cancel="handleCancel" cancelText="关闭" style="top:20px;">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item label="行程详情" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <span>{{ model.id }} | {{ model.tripName }} | {{ model.tripFullName }}</span>
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="文件类型">
            <a-radio-group v-model="exportFile.fileType" :options="filteredFileTypeOptions"
              :default-value="exportFile.fileType" @change="handleFileTypeChange" />
          </a-form-item>
          <a-form-item v-if="showPriceOption" :labelCol="labelCol" :wrapperCol="wrapperCol" label="分项报价">
            <a-radio-group v-model="exportFile.hasPrice" :options="exportFile.options"
              :default-value="exportFile.hasPrice" />
          </a-form-item>
          <a-form-item v-if="showStyleOption" :labelCol="labelCol" :wrapperCol="wrapperCol" label="风格样式">
            <a-radio-group v-model="exportFile.styleID" class="style-radio-group">
              <a-radio v-for="style in currentStyleList" :key="style.value" :value="style.value">
                {{ style.label }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="showShopStyleOption" :labelCol="labelCol" :wrapperCol="wrapperCol" label="店铺样式">
            <a-select :allow-clear="true" show-search placeholder="店铺名称/登录账号" option-filter-prop="children"
              style="width: 300px" :default-active-first-option="false" :show-arrow="false" :filter-option="false"
              :not-found-content="null" @search="handleSearchShop" @change="handleUserClick"
              v-model="currentShop.loginName">
              <a-select-option v-for="item in shopList" :key="item.loginName" :value="item.loginName">
                {{ item.shopName }} {{ item.loginName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="exportFile.fileType === 'excel'" :labelCol="labelCol" :wrapperCol="wrapperCol"
            label="出发日期">
            <a-date-picker v-model="exportFile.extraParams.departureDate" style="width: 100%" />
          </a-form-item>
          <a-form-item v-if="exportFile.fileType === 'excel'" :labelCol="labelCol" :wrapperCol="wrapperCol" label="大人数">
            <a-input-number v-model="exportFile.extraParams.adultCount" :min="1" style="width: 100%" />
          </a-form-item>
          <a-form-item v-if="exportFile.fileType === 'excel'" :labelCol="labelCol" :wrapperCol="wrapperCol" label="孩子数">
            <a-input-number v-model="exportFile.extraParams.childCount" :min="0" style="width: 100%" />
          </a-form-item>
          <a-form-item v-if="exportFile.fileType === 'excel'" :labelCol="labelCol" :wrapperCol="wrapperCol" label="房间数">
            <a-input-number v-model="exportFile.extraParams.roomCount" :min="1" style="width: 100%" />
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
    <a-modal :visible="exportingModalVisible" :footer="null" :closable="false" width="300px">
      <div style="text-align: center;">
        <a-spin size="large" />
        <h3 style="margin-top: 16px;">正在导出文件...</h3>
        <a-progress :percent="exportProgress" status="active" />
        <p style="margin-top: 8px;">已耗时: {{ exportTime }} 秒</p>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getAction } from '@/api/manage';
import { JeecgListMixin } from '@/mixins/JeecgListMixinCopy';
import Vue from 'vue';
import { ACCESS_TOKEN } from '@/store/mutation-types';

export default {
  name: 'ExportWordModal',
  mixins: [JeecgListMixin],
  data() {
    return {
      visible: false,
      confirmLoading: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      form: this.$form.createForm(this),
      shopList: [],
      currentShop: {
        shopName: "请选择",
        loginName: null,
      },
      exportFile: {
        hasPrice: 'true',
        fileType: 'word',
        id: null,
        options: [
          { label: '包含', value: 'true' },
          { label: '不包含', value: 'false' },
        ],
        FileTypeOptions: [
          { label: 'WORD', value: 'word' },
          { label: 'PDF', value: 'pdf' },
          { label: 'PPT', value: 'ppt' },
          { label: 'EXCEL报价单', value: 'excel' },
        ],
        styleID: 1,
        extraParams: {
          departureDate: null,
          adultCount: 2,
          childCount: 1,
          roomCount: 1,
        }
      },
      exportSettings: {
        styleID: 1,
        fileType: 'word',
        hasPrice: 'true'
      },
      isEnableExcelDownload: false,
      isEnablePPTDownload: false,
      sysUser: {},
      url: {
        exportFile: "biz/bizTrip/exportFile",
        getCurrentShop: "biz/bizShop/getCurrentShop",
        getCom: "/biz/bizCompany/queryById",
        list: "/biz/bizTrip/list"
      },
      exportingModalVisible: false,
      exportProgress: 0,
      exportTime: 0,
      exportTimer: null,
      companyConfig: null,
      wordStyleList: [],
      pptStyleList: [],
    };
  },
  computed: {
    filteredFileTypeOptions() {
      return this.exportFile.FileTypeOptions.filter(option =>
        (this.isEnableExcelDownload || option.value !== 'excel') &&
        (this.isEnablePPTDownload || option.value !== 'ppt')
      );
    },
    showPriceOption() {
      return ['word', 'pdf'].includes(this.exportFile.fileType);
    },
    showStyleOption() {
      return ['word', 'pdf', 'ppt'].includes(this.exportFile.fileType);
    },
    showShopStyleOption() {
      return ['word', 'pdf', 'ppt'].includes(this.exportFile.fileType) && 
             this.sysUser && 
             this.sysUser.roleCode && 
             (this.sysUser.roleCode === 'admin' || this.sysUser.roleCode.startsWith('biz'));
    },
    currentStyleList() {
      switch(this.exportFile.fileType) {
        case 'word':
        case 'pdf':
          return this.wordStyleList.length > 0 ? this.wordStyleList : [{ label: '默认', value: 1 }];
        case 'ppt':
          return this.pptStyleList.length > 0 ? this.pptStyleList : [{ label: '默认', value: 1 }];
        default:
          return [{ label: '默认', value: 1 }];
      }
    }
  },
  created() {
    this.sysUser = this.$store.getters.userInfo;
    if (this.sysUser.roleCode.startsWith('biz')) {
      this.getCompanyConfig(this.sysUser.comId);
    }
  },
  methods: {
    getCompanyConfig(comId) {
      if (!comId) return;
      getAction(this.url.getCom, { id: comId })
        .then((res) => {
          if (res.success) {
            this.companyConfig = res.result;
            this.updateExportOptions();
          } else {
            this.$message.warning(res.message || '获取公司配置失败');
          }
        })
        .catch((error) => {
          console.error('获取公司配置出错', error);
          this.$message.error('获取公司配置失败，请重试');
        });
    },
    updateExportOptions() {
      if (this.companyConfig && this.companyConfig.config) {
        let config = JSON.parse(this.companyConfig.config);
        this.isEnableExcelDownload = config.enableExcelDownload;
        this.isEnablePPTDownload = config.enablePPTDownload;

        if (!this.sysUser.roleCode.startsWith('biz')) {
          this.isEnableExcelDownload = this.isEnableExcelDownload && this.sysUser.roleCode === 'shopSuper';
          this.isEnablePPTDownload = this.isEnablePPTDownload && this.sysUser.roleCode === 'shopSuper';
        }
      } else {
        this.isEnableExcelDownload = false;
        this.isEnablePPTDownload = false;
      }
      this.updateFileTypeOptions();
    },
    updateFileTypeOptions() {
      this.exportFile.FileTypeOptions = this.exportFile.FileTypeOptions.filter(option => 
        (this.isEnableExcelDownload || option.value !== 'excel') &&
        (this.isEnablePPTDownload || option.value !== 'ppt')
      );
    },
    handleSearchShop(value) {
      if (!value) return;
      getAction(`/biz/bizShop/list`, { "searchValue": value }).then(res => {
        if (res.success) {
          this.shopList = res.result.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getTipStatus() {
      let text;
      if (this.model.status == 1) {
        text = "公开";
      } else if (this.model.status == 2) {
        text = "已删除";
      } else if (this.model.status == 3) {
        text = "定制";
      } else {
        text = "其他";
      }
      return text;
    },
    show(record) {
      this.visible = true;
      this.model = record;

      // 解析 Word 样式
      if (record.companyWordStyle) {
        try {
          const parsedWordStyle = JSON.parse(record.companyWordStyle);
          this.wordStyleList = parsedWordStyle.map(value => ({ label: value.styleName, value: value.id }));
        } catch (e) {
          console.error("解析 companyWordStyle 失败", e);
          this.wordStyleList = [];
        }
      } else {
        this.wordStyleList = [];
      }

      // 解析 PPT 样式
      if (record.companyPptStyle) {
        try {
          const parsedPptStyle = JSON.parse(record.companyPptStyle);
          this.pptStyleList = parsedPptStyle.map(value => ({ label: value.styleName, value: value.id }));
        } catch (e) {
          console.error("解析 pptStyle 失败", e);
          this.pptStyleList = [];
        }
      } else {
        this.pptStyleList = [];
      }

      // 加载保存的设置
      const savedSettings = localStorage.getItem('exportSettings_' + this.model.comId);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        this.exportSettings = { ...this.exportSettings, ...parsedSettings };
        this.exportFile.styleID = this.exportSettings.styleID;
        this.exportFile.fileType = this.exportSettings.fileType;
        this.exportFile.hasPrice = this.exportSettings.hasPrice;
      }

      // 验证 fileType 是否在可用选项中
      const fileTypeOptions = this.exportFile.FileTypeOptions.map(option => option.value);
      if (!fileTypeOptions.includes(this.exportFile.fileType)) {
        this.exportFile.fileType = 'word';
      }
      // 处理公司配置
      if (this.sysUser.roleCode === 'shopSuper' && this.model.comId
        && (this.companyConfig == null || this.companyConfig.id != this.model.comId)) {
        this.getCompanyConfig(this.model.comId);
      }
    },
    close() {
      this.$emit('close');
      this.visible = false;
      // 重置状态
      this.exportSettings = {
        styleID: 1,
        fileType: 'word',
        hasPrice: 'true'
      };
      this.exportFile = {
        hasPrice: 'true',
        fileType: 'word',
        id: null,
        options: [
          { label: '包含', value: 'true' },
          { label: '不包含', value: 'false' },
        ],
        FileTypeOptions: [
          { label: 'WORD', value: 'word' },
          { label: 'PDF', value: 'pdf' },
          { label: 'PPT', value: 'ppt' },
          { label: 'EXCEL报价单', value: 'excel' },
        ],
        styleID: 1,
        extraParams: {
          departureDate: null,
          adultCount: 1,
          childCount: 0,
          roomCount: 1,
        }
      };
    },
    handleUserClick(value) {
      this.currentShop.loginName = value;
    },
    handleCancel() {
      this.close();
    },
    isWechatBrowser() {
      const ua = navigator.userAgent.toLowerCase();
      return ua.match(/MicroMessenger/i) == "micromessenger";
    },
    handleFileTypeChange() {
      // 当文件类型改变时，重置样式ID为默认值
      this.exportFile.styleID = 1;
    },
    onExportFile() {
      if (this.exportFile.fileType === 'excel') {
        if (
          this.exportFile.extraParams.departureDate === null ||
          this.exportFile.extraParams.adultCount === null ||
          this.exportFile.extraParams.childCount === null ||
          this.exportFile.extraParams.roomCount === null
        ) {
          this.$message.error('出发日期、大人数、孩子数和房间数必须填写');
          return;
        }
      }
      // 更新并保存设置
      this.exportSettings.styleID = this.exportFile.styleID;
      this.exportSettings.fileType = this.exportFile.fileType;
      this.exportSettings.hasPrice = this.exportFile.hasPrice;
      localStorage.setItem('exportSettings_' + this.model.comId, JSON.stringify(this.exportSettings));

      const extraParams = this.exportFile.fileType === 'excel' ? JSON.stringify(this.exportFile.extraParams) : '';

      let exportUrl = `${window._CONFIG['domianURL']}/${this.url.exportFile}?id=${this.model.id}&fileType=${this.exportFile.fileType}&hasPrice=${this.exportFile.hasPrice}&styleID=${this.exportFile.styleID}&extraParams=${extraParams}&shopLoginName=${this.currentShop.loginName}`;

      this.exportingModalVisible = true;
      this.exportProgress = 0;
      this.exportTime = 0;
      
      // 启动计时器
      if (this.exportTimer) {
        clearInterval(this.exportTimer);
      }
      this.exportTimer = setInterval(() => {
        this.exportTime++;
      }, 1000);

      const simulateProgress = () => {
        if (this.exportProgress < 90) {
          this.exportProgress += Math.floor(Math.random() * 10) + 1;
          setTimeout(simulateProgress, 200);
        }
      };
      simulateProgress();

      if (this.isWechatBrowser()) {
        const token = Vue.ls.get(ACCESS_TOKEN);
        exportUrl += `&token=${token}`;
        this.$message.info('文件准备完成,请在新页面中查看');
        window.open(exportUrl, '_blank');
        this.exportingModalVisible = false;
      } else {
        fetch(exportUrl, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'download';
            if (contentDisposition) {
              const filenameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/i);
              if (filenameMatch && filenameMatch[1]) {
                filename = decodeURIComponent(filenameMatch[1]);
              }
            }
            return response.blob().then(blob => ({ blob, filename }));
          })
          .then(({ blob, filename }) => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            this.exportProgress = 100;
            setTimeout(() => {
              this.exportingModalVisible = false;
              this.stopExportTimer();
              this.$message.success('文件导出成功!');
            }, 500);
          })
          .catch(error => {
            console.error('Download error:', error);
            this.exportingModalVisible = false;
            this.stopExportTimer();
            this.$message.error('下载失败，请重试或联系管理员');
          });
      }
    },
    stopExportTimer() {
      if (this.exportTimer) {
        clearInterval(this.exportTimer);
        this.exportTimer = null;
      }
    },
  }
};
</script>

<style scoped>
.style-radio-group .ant-radio-wrapper {
  margin-right: 12px;
  margin-bottom: 8px;
  line-height: 2;
}
</style>