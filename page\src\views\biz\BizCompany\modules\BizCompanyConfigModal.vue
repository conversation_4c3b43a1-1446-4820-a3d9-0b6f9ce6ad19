<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="公司名称">
          {{model.companyName}}
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="行程展示类型">
          <a-select v-model="model.companyType" style="width: 200px">
            <a-select-option :value="1">折叠景点（长线）</a-select-option>
            <a-select-option :value="2">平铺图文（周边）</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否允许店铺下载报价">
          <a-switch v-model="model.allowShopDownloadPrice" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否开启成本填空">
          <a-switch v-model="model.enableCostInput" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否开启excel下载">
          <a-switch v-model="model.enableExcelDownload" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否开启PPT下载">
          <a-switch v-model="model.enablePPTDownload" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="是否启用小程序H5功能">
          <a-switch v-model="model.enableH5" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="限制账号单人登录">
          <a-switch v-model="model.singleLogin" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="默认行程编辑模式">
          <a-select v-model="model.defaultEditMode" style="width: 200px">
            <a-select-option value="standard">标准模式</a-select-option>
            <a-select-option value="export">快捷导出</a-select-option>
            <a-select-option value="h5">小程序H5</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage';
  import { axios } from '@/utils/request';

  export default {
    name: "BizCompanyConfigModal",
    data() {
      return {
        title: "配置中心",
        visible: false,
        model: {
          companyName: null,
          companyType: 1,//行程展示类型 1:折叠景点（长线） 2:平铺图文（周边）
          allowShopDownloadPrice: true,
          enableCostInput: false,
          enableExcelDownload: true,
          enablePPTDownload: true,
          enableH5: true,
          singleLogin: false,
          defaultEditMode: 'export'
        },
        labelCol: { xs: { span: 24 }, sm: { span: 5 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        confirmLoading: false,
        form: this.$form.createForm(this),
        url: { edit: "/biz/bizCompany/edit" }
      };
    },
    methods: {
      show(record) {
        this.edit(record);
      },
      edit(record) {
        this.model.id = record.id;
        this.model.companyName = record.companyName;
        if (record.config) {
          const config = JSON.parse(record.config);
          this.model.companyType = config.companyType || 1;
          this.model.allowShopDownload = config.allowShopDownload || false;
          this.model.enableCostInput = config.enableCostInput || false;
          this.model.enableExcelDownload = config.enableExcelDownload || true;
          this.model.enablePPTDownload = config.enablePPTDownload || true;
          this.model.enableH5 = config.enableH5 || true;
          this.model.singleLogin = config.singleLogin || false;
          this.model.defaultEditMode = config.defaultEditMode || 'export';
        }
        this.visible = true;
      },
      close() {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        const that = this;
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            if (!this.model.id) {
              that.$message.error('公司ID不存在');
              return;
            }
            this.model.config = JSON.stringify({
              companyType: this.model.companyType,
              allowShopDownloadPrice: this.model.allowShopDownloadPrice,
              enableCostInput: this.model.enableCostInput,
              enableExcelDownload: this.model.enableExcelDownload,
              enablePPTDownload: this.model.enablePPTDownload,
              enableH5: this.model.enableH5,
              singleLogin: this.model.singleLogin,
              defaultEditMode: this.model.defaultEditMode
            });
            httpAction(this.url.edit, this.model, 'put').then(res => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            });
          }
        });
      },
      handleCancel() {
        this.close();
      }
    }
  };
</script>

<style scoped>
</style>