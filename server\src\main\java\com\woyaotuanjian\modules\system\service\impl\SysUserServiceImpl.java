package com.woyaotuanjian.modules.system.service.impl;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.util.PasswordUtil;
import com.woyaotuanjian.modules.biz.mapper.CommonMapper;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import com.woyaotuanjian.modules.system.entity.*;
import com.woyaotuanjian.modules.system.mapper.*;
import com.woyaotuanjian.modules.system.service.ISysUserService;
import com.woyaotuanjian.common.system.api.ISysBaseAPI;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.common.system.vo.SysUserCacheInfo;
import com.woyaotuanjian.common.util.OConvertUtils;
import com.woyaotuanjian.common.util.RedisUtil;
import com.woyaotuanjian.common.constant.CommonConstant;
import com.woyaotuanjian.common.system.util.JwtUtil;
import com.woyaotuanjian.common.util.UserSessionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * @Author: scott
 * @Date: 2018-12-20
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {
	
	@Autowired
	private SysUserMapper userMapper;
	@Autowired
	private SysPermissionMapper sysPermissionMapper;
	@Autowired
	private SysUserRoleMapper sysUserRoleMapper;
	@Autowired
	private ISysBaseAPI sysBaseAPI;
	@Autowired
	private SysDepartMapper sysDepartMapper;
	@Autowired
	private SysRoleMapper sysRoleMapper;
	@Autowired
	private CommonMapper commonMapper;
	@Autowired
	private RedisUtil redisUtil;
	@Autowired
	private UserSessionUtil userSessionUtil;


	@Override
	@Transactional(readOnly = true)
	public Page getExtPageList(Page page, Map<String, Object> map) {
		page.setRecords(userMapper.getExtList(map));
		page.setTotal(commonMapper.getCount());
		return page;
	}

	@Override
	public SysUser getUserByName(String username) {
		return userMapper.getUserByName(username);
	}
	
	
	@Override
	@Transactional
	public void addUserWithRole(SysUser user, String roles) {
		this.save(user);
		if(OConvertUtils.isNotEmpty(roles)) {
			String[] arr = roles.split(",");
			for (String roleId : arr) {
				SysUserRole userRole = new SysUserRole(user.getId(), Integer.valueOf(roleId));
				sysUserRoleMapper.insert(userRole);
			}
		}
	}

	@Override
	@Transactional
	public Result registerShopUser(String loginName, String password, String nickName) {
		SysUser dbUser=getUserByName(loginName);
		if(dbUser!=null){
			return Result.error("该账号已注册");
		}
		LoginUser currentUser= SysUserUtil.getCurrentUser();
		SysUser newUser = new SysUser();
		newUser.setUsername(loginName);
		newUser.setRealname(nickName);
		newUser.setCreateTime(new Date());//设置创建时间
		newUser.setUpdateTime(new Date());
		newUser.setCreateBy(currentUser.getRealname());
		String salt = OConvertUtils.randomGen(8);
		newUser.setSalt(salt);
		String passwordEncode = PasswordUtil.encrypt(loginName, password, salt);
		newUser.setPassword(passwordEncode);
		newUser.setStatus(1);
		newUser.setRoleCode(RoleConstant.B2);
		this.save(newUser);
		//固定死是shop角色
		SysRole sysRole= sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode,"shop").last("limit 1"));
		SysUserRole sysUserRole=new SysUserRole();
		sysUserRole.setUserId(newUser.getId());
		sysUserRole.setRoleId(sysRole.getId());
		sysUserRoleMapper.insert(sysUserRole);
		return Result.ok();
	}

	@Override
	@Transactional
	public Result updateShopUserPassword(String loginName, String password) {
		SysUser dbUser=getUserByName(loginName);
		if(dbUser==null){
			return Result.error("该账号不存在");
		}
		if(YdUtil.empty(dbUser.getRoleCode())||(!dbUser.getRoleCode().equals(RoleConstant.B2) && !dbUser.getRoleCode().equals(RoleConstant.B2SUPER))){
			return Result.error("该账号不是店铺关联账号");
		}
		dbUser.setPassword( PasswordUtil.encrypt(loginName, password, dbUser.getSalt()));
		dbUser.setUpdateTime(new Date());
		updateById(dbUser);
		
		// 修改密码后踢出该用户的所有登录会话
		int kickedCount = userSessionUtil.kickOutUser(loginName);
		
		return Result.ok("密码修改完成！已踢出 " + kickedCount + " 个登录会话，用户需要重新登录。");
	}

	@Override
	@CacheEvict(value="loginUser_cacheRules", allEntries=true)
	@Transactional
	public void editUserWithRole(SysUser user, String roles) {
		this.updateById(user);
		//先删后加
		sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
		if(OConvertUtils.isNotEmpty(roles)) {
			String[] arr = roles.split(",");
			for (String roleId : arr) {
				SysUserRole userRole = new SysUserRole(user.getId(), Integer.valueOf(roleId));
				sysUserRoleMapper.insert(userRole);
			}
		}
	}


	@Override
	public List<String> getRole(String username) {
		return sysUserRoleMapper.getRoleByUserName(username);
	}
	
	/**
	 * 通过用户名获取用户角色集合
	 * @param username 用户名
     * @return 角色集合
	 */
	@Override
	@Cacheable(value = "loginUser_cacheRules",key = "'Roles_'+#username")
	public Set<String> getUserRolesSet(String username) {
		// 查询用户拥有的角色集合
		List<String> roles = sysUserRoleMapper.getRoleByUserName(username);
		log.info("-------通过数据库读取用户拥有的角色Rules------username： " + username + ",Roles size: " + (roles == null ? 0 : roles.size()));
		return new HashSet<>(roles);
	}

	/**
	 * 通过用户名获取用户权限集合
	 *
	 * @param username 用户名
	 * @return 权限集合
	 */
	@Override
	@Cacheable(value = "loginUser_cacheRules",key = "'Permissions_'+#username")
	public Set<String> getUserPermissionsSet(String username) {
		Set<String> permissionSet = new HashSet<>();
		List<SysPermission> permissionList = sysPermissionMapper.queryByUser(username);
		for (SysPermission po : permissionList) {
//			// TODO URL规则有问题？
//			if (oConvertUtils.isNotEmpty(po.getUrl())) {
//				permissionSet.add(po.getUrl());
//			}
			if (OConvertUtils.isNotEmpty(po.getPerms())) {
				permissionSet.add(po.getPerms());
			}
		}
		log.info("-------通过数据库读取用户拥有的权限Perms------username： "+ username+",Perms size: "+ (permissionSet==null?0:permissionSet.size()) );
		return permissionSet;
	}

	@Override
	public SysUserCacheInfo getCacheUser(String username) {
		SysUserCacheInfo info = new SysUserCacheInfo();
		info.setOneDepart(true);
//		SysUser user = userMapper.getUserByName(username);
//		info.setSysUserCode(user.getUsername());
//		info.setSysUserName(user.getRealname());
		

		LoginUser user = sysBaseAPI.getUserByName(username);
		if(user!=null) {
			info.setSysUserCode(user.getUsername());
			info.setSysUserName(user.getRealname());
			info.setSysOrgCode(user.getOrgCode());
		}
		
		//多部门支持in查询
		List<SysDepart> list = sysDepartMapper.queryUserDeparts(user.getId());
		List<String> sysMultiOrgCode = new ArrayList<String>();
		if(list==null || list.size()==0) {
			//当前用户无部门
			//sysMultiOrgCode.add("0");
		}else if(list.size()==1) {
			sysMultiOrgCode.add(list.get(0).getOrgCode());
		}else {
			info.setOneDepart(false);
			for (SysDepart dpt : list) {
				sysMultiOrgCode.add(dpt.getOrgCode());
			}
		}
		info.setSysMultiOrgCode(sysMultiOrgCode);
		
		return info;
	}

	// 根据部门Id查询
	@Override
	public IPage<SysUser> getUserByDepId(Page<SysUser> page, String departId,String username) {
		return userMapper.getUserByDepId(page, departId,username);
	}


	// 根据角色Id查询
	@Override
	public IPage<SysUser> getUserByRoleId(Page<SysUser> page, String roleId, String username) {
		return userMapper.getUserByRoleId(page,roleId,username);
	}


	@Override
	public void updateUserDepart(String username,String orgCode) {
		baseMapper.updateUserDepart(username, orgCode);
	}

	@Override
        public int kickOutUser(String username) {
                return userSessionUtil.kickOutUser(username);
        }

        @Override
        public int countB1UsersByComId(Long comId) {
                return this.count(new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getComId, comId)
                        .likeRight(SysUser::getRoleCode, RoleConstant.B1));
        }

}
