package com.woyaotuanjian.modules.biz.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Date;

import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizCompany;
import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.entity.pojo.BizCompanyExt;
import com.woyaotuanjian.modules.biz.mapper.BizCompanyMapper;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.mapper.CommonMapper;

import com.woyaotuanjian.modules.biz.service.IBizCompanyShopService;
import com.woyaotuanjian.modules.biz.service.IBizTripService;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class BizCompanyServiceImpl extends ServiceImpl<BizCompanyMapper, BizCompany> implements IBizCompanyService {

	private static final Logger log = LoggerFactory.getLogger(BizCompanyServiceImpl.class);

	@Autowired
    BizCompanyMapper bizCompanyMapper;
    @Autowired
    CommonMapper commonMapper;
    @Autowired
    IBizCompanyShopService companyShopService;
    @Autowired
    IBizTripService tripService;

	@Override
	@Transactional(readOnly = true)
	public Page getBizCompanyExtList(Page page, Map<String, Object> map) {
        page.setRecords(bizCompanyMapper.getBizCompanyExtList(map));
        page.setTotal(commonMapper.getCount());
        return page;
    }

    @Override
    public void dataListFilter(Map<String, Object> param, LoginUser sysUser, Integer onlyCheckedCom) {
        //关联公司id列表
        List<Long> comIdList=new ArrayList<>(10);
        if((param.get("type")==null)){ //如果不是景点，都要加入基础数据,目前只有景区有type
            comIdList.add(0L);//admin的数据也加入，admin产生的数据，comId=0
        }
        if(sysUser.getRoleCode().equals(RoleConstant.ADMIN)){//管理员
            //不限
        }else if(sysUser.getRoleCode().startsWith(RoleConstant.B1)){//b1或者b1super
            //某个com的员工，仅能看自己com或admin的数据
            comIdList.add(sysUser.getComId());
            //先加入com约束
            param.put("comGroup",YdUtil.sqlJoin(comIdList));
        }else if(sysUser.getRoleCode().startsWith(RoleConstant.B2)){//b2或者b2super
            //b2的基础数据，可以看到已勾选公司+admin的数据+自己的私有数据
            List<BizCompanyExt> comList=companyShopService.getShopComList(sysUser.getUsername());
            if(!CollectionUtils.isEmpty(comList)){
                List<Long> filtedComIdList = null;
                if(onlyCheckedCom.equals(1)){//只要已勾选的com
                    filtedComIdList=comList.stream()
                            .filter(item->item.getComShopStatus().equals("2"))
                            .map(BizCompanyExt::getId)
                            .collect(Collectors.toList());

                }else if(onlyCheckedCom.equals(2)){//要全部com
                    filtedComIdList=comList.stream()
                            .map(BizCompanyExt::getId)
                            .collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(filtedComIdList)){
                    comIdList.addAll(filtedComIdList);
                }
            }

            //先加入com约束
            param.put("comGroup", YdUtil.sqlJoin(comIdList));
            //自己的数据
            param.put("currentUserId",sysUser.getId());
        }
    }

    @Override
    public void panelDataListFilter(Map<String, Object> param, LoginUser sysUser) {

        //关联公司id列表
        List<Long> comIdList=new ArrayList<>(10);

        if((param.get("type")==null)){ //如果不是景点，都要加入基础数据,目前只有景区有type
            comIdList.add(0L);//admin的数据也加入，admin产生的数据，comId=0
        }else{ //景点只有在勾选时才加入
            if ((param.get("systemScenicDataChecked")!=null) &&("true".equals(param.get("systemScenicDataChecked")))){
                comIdList.add(0L);//admin的数据也加入，admin产生的数据，comId=0
            }
        }

        if(sysUser.getRoleCode().equals(RoleConstant.ADMIN)){//管理员
            //自己的基础数据
            param.put("comGroup",YdUtil.sqlJoin(comIdList));
        }else if(sysUser.getRoleCode().startsWith(RoleConstant.B1)){//b1或者b1super
            //某个com的员工，仅能看自己com或admin的数据
            comIdList.add(sysUser.getComId());
            //先加入com约束
            param.put("comGroup",YdUtil.sqlJoin(comIdList));
        }else if(sysUser.getRoleCode().startsWith(RoleConstant.B2)){//b2或者b2super
            if(param!=null&&YdUtil.isDigit(param.get("tripId")+"")){
                //编辑模式，基础数据=admin+行程所属com+b2私有数据
                String tripId=param.get("tripId")+"";
                BizTrip trip=tripService.getById(tripId);
                if(trip!=null){
                    //trip所属的comId
                    comIdList.add(trip.getComId());
                    param.put("comGroup", YdUtil.sqlJoin(comIdList));
                }
            }else{
                //新增模式，基础数据=admin+默认com+b2私有数据
                List<BizCompanyExt> comList=companyShopService.getShopComList(sysUser.getUsername());
                if(!CollectionUtils.isEmpty(comList)){
                    List<Long> filtedComIdList=comList.stream()
                                .filter(item->item.getDefaultCom().equals("2"))
                                .map(BizCompanyExt::getId)
                                .collect(Collectors.toList());
                    //添加默认公司
                    if(!CollectionUtils.isEmpty(filtedComIdList)){
                        comIdList.addAll(filtedComIdList);
                        param.put("comGroup", YdUtil.sqlJoin(comIdList));
                    }
                }
            }
            //自己的数据
            param.put("currentUserId",sysUser.getId());
        }
    }

    @Override
    public void tripListFilter(Map<String, Object> param, LoginUser sysUser, Integer onlyCheckedCom) {
        //关联公司id列表
        List<Long> comIdList=new ArrayList<>(10);

        if(sysUser.getRoleCode().equals(RoleConstant.ADMIN)){//管理员
            //不限
        }else if(sysUser.getRoleCode().startsWith(RoleConstant.B1)){//b1或者b1super
            //某个com的员工，仅能看自己com或admin的数据
            comIdList.add(sysUser.getComId());
            //先加入com约束
            param.put("comGroup",YdUtil.sqlJoin(comIdList));
            if(onlyCheckedCom.equals(1)){//只要com员工做的行程，不含shop的行程
                //加入role约束
                List<String> roleList=new ArrayList<>(3);
                roleList.add(RoleConstant.ADMIN);
                roleList.add(RoleConstant.B1);
                roleList.add(RoleConstant.B1SUPER);
                param.put("roleGroup", YdUtil.sqlJoin(roleList));
            }else if(onlyCheckedCom.equals(2)){//本com的全部行程

            }

        }else if(sysUser.getRoleCode().startsWith(RoleConstant.B2)){//b2或者b2super
            //b2的行程列表，可以看到已勾选公司+admin的数据
            List<BizCompanyExt> comList=companyShopService.getShopComList(sysUser.getUsername());
            if(!CollectionUtils.isEmpty(comList)){
                List<Long> filtedComIdList = null;
                if(onlyCheckedCom.equals(1)){//只要已勾选的com
                    filtedComIdList=comList.stream()
                            .filter(item->item.getComShopStatus().equals("2"))
                            .map(BizCompanyExt::getId)
                            .collect(Collectors.toList());

                }else if(onlyCheckedCom.equals(2)){//要全部com
                    filtedComIdList=comList.stream()
                            .map(BizCompanyExt::getId)
                            .collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(filtedComIdList)){
                    comIdList.addAll(filtedComIdList);
                }
            }

            //加入com约束
            param.put("comGroup", YdUtil.sqlJoin(comIdList));
            //加入role约束
            List<String> roleList=new ArrayList<>(3);
            roleList.add(RoleConstant.ADMIN);
            roleList.add(RoleConstant.B1);
            roleList.add(RoleConstant.B1SUPER);
            param.put("roleGroup", YdUtil.sqlJoin(roleList));
            //b2标志
            param.put("b2Flag", 1);
        }
    }

    @Override
    public Result checkBasicDataPermission(LoginUser loginUser, Long comId, Integer authorId) {
        if(loginUser.getRoleCode().equals(RoleConstant.ADMIN)){//管理员
            //不限制
        }else if(loginUser.getRoleCode().startsWith(RoleConstant.B1)){//b1或者b1super
            if(comId==null||!loginUser.getComId().equals(comId)){//不是本公司数据
                //不是本公司数据
                return Result.error("权限不足");
            }
        }else if(loginUser.getRoleCode().startsWith(RoleConstant.B2)) {//b2或者b2super
            if(authorId==null||!loginUser.getId().equals(authorId)){
                //不是本人数据
                return Result.error("权限不足");
            }
        }
        return Result.ok();
    }

    @Override
    public Integer getCompanyType(String id) {
        BizCompany bizCompany = this.getById(id);
        if (bizCompany == null || bizCompany.getConfig() == null) {
            return 1; // 默认返回1
        }
        
        try {
            JSONObject configJson = JSONObject.parseObject(bizCompany.getConfig());
            Integer companyType = configJson.getInteger("companyType");
            return companyType == null ? 1 : companyType; // 如果为null则返回默认值1
        } catch (Exception e) {
            log.error("解析公司配置信息失败", e);
            return 1; // 解析失败时返回默认值1
        }
    }

    @Override
    public boolean updateMemberInfo(BizCompany bizCompany) {
        if (bizCompany == null || bizCompany.getId() == null) {
            log.error("更新会员信息失败：参数为空或ID为空");
            return false;
        }
        
        try {
            // 获取原有公司信息
            BizCompany dbCompany = this.getById(bizCompany.getId());
            if (dbCompany == null) {
                log.error("更新会员信息失败：未找到ID为{}的公司", bizCompany.getId());
                return false;
            }
            
            // 只更新会员相关字段
            BizCompany updateCompany = new BizCompany();
            updateCompany.setId(bizCompany.getId());
            updateCompany.setMemberGrade(bizCompany.getMemberGrade());
            updateCompany.setMaxAccountNum(bizCompany.getMaxAccountNum());
            updateCompany.setExpireTime(bizCompany.getExpireTime());
            updateCompany.setAdminRemark(bizCompany.getAdminRemark());
            updateCompany.setContactPerson(bizCompany.getContactPerson());
            updateCompany.setContactPhone(bizCompany.getContactPhone());
            updateCompany.setWechat(bizCompany.getWechat());
            updateCompany.setInvoiceName(bizCompany.getInvoiceName());
            updateCompany.setAttach(bizCompany.getAttach());
            updateCompany.setUpdateTime(new Date());
            
            log.info("更新会员信息：公司ID={}, 会员等级={}, 到期时间={}, 联系人={}, 联系电话={}", 
                    bizCompany.getId(), 
                    bizCompany.getMemberGrade(), 
                    bizCompany.getExpireTime(),
                    bizCompany.getContactPerson(),
                    bizCompany.getContactPhone());
            
            // 更新到数据库
            return this.updateById(updateCompany);
        } catch (Exception e) {
            log.error("更新会员信息出现异常", e);
            return false;
        }
    }
}
