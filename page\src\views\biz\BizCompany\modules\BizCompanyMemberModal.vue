<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
      
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="公司">
          {{model.companyName}}
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="简码">
          {{model.companyCode}}
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="会员等级">
          <j-dict-select-tag
            style="width:100%;"
            v-model="model.memberGrade"
            placeholder="请选择会员等级"
            dictCode="biz_member_grade"
          />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="最大账号数">
          <a-input-number v-model="model.maxAccountNum" :min="0" style="width:100%;" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="到期时间">
          <a-date-picker 
            v-model="model.expireTime" 
            style="width:100%;"
            :showTime="false"
            format="YYYY-MM-DD"
            placeholder="请选择到期时间" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="管理备注">
          <a-textarea 
            v-model="model.adminRemark" 
            placeholder="请输入管理备注"
            :maxLength="256"
            :rows="3" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="联系人">
          <a-input 
            v-model="model.contactPerson" 
            placeholder="请输入联系人姓名" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="联系电话">
          <a-input 
            v-model="model.contactPhone" 
            placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="微信">
          <a-input 
            v-model="model.wechat" 
            placeholder="请输入微信号" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="开票名称">
          <a-input
            v-model="model.invoiceName"
            placeholder="请输入开票名称" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="附件">
          <a-upload name="file" :action="uploadUrl" :fileList="fileUpload.fileList"
            :beforeUpload="beforeFileUpload" @change="handleFileChange"
            :showUploadList="{ showRemoveIcon: true, showPreviewIcon: true }"
            multiple>
            <a-button>
              <a-icon type="upload" />
              上传附件（支持图片和文档）
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import moment from "moment"
  import { axios } from '@/utils/request';

  export default {
    name: "BizCompanyMemberModal",
    data () {
      return {
        title:"会员管理",
        visible: false,
        model: {
          id: '',
          companyName: '',
          companyCode: '',
          memberGrade: '',
          maxAccountNum: null,
          expireTime: null,
          adminRemark: '',
          contactPerson: '',
          contactPhone: '',
          wechat: '',
          invoiceName: '',
          attach: null
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        fileUpload: {
          fileList: []
        },
        url: {
          edit: "/biz/bizCompany/editMember",
        }
      }
    },
    created () {
    },
    computed: {
      uploadUrl() {
        return (axios.defaults.baseURL || '') + '/file/upload'
      }
    },
    methods: {
      show (record) {
        this.edit(record);
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        if (this.model.attach) {
          try {
            this.fileUpload.fileList = JSON.parse(this.model.attach);
          } catch (e) {
            this.fileUpload.fileList = [];
          }
        } else {
          this.fileUpload.fileList = [];
        }

        // Convert memberGrade to string for j-dict-select-tag
        if (this.model.memberGrade !== null && this.model.memberGrade !== undefined) {
          this.model.memberGrade = String(this.model.memberGrade);
        }

        // 处理日期格式
        if(this.model.expireTime) {
          this.model.expireTime = moment(this.model.expireTime);
        }
        
        this.visible = true;
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;

        try {
          // 处理时间格式化
          let formData = Object.assign({}, this.model);
          if(formData.expireTime) {
            formData.expireTime = formData.expireTime.format('YYYY-MM-DD');
            console.log('格式化后的日期：', formData.expireTime);
          }

          if (this.fileUpload.fileList && this.fileUpload.fileList.length > 0) {
            formData.attach = JSON.stringify(this.fileUpload.fileList);
          } else {
            formData.attach = null;
          }

          that.confirmLoading = true;
          httpAction(this.url.edit, formData, 'put').then((res) => {
            if(res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message || '更新失败');
            }
          }).catch((err) => {
            console.error('会员信息更新异常', err);
            that.$message.error('更新失败：' + (err.message || '服务器异常'));
          }).finally(() => {
            that.confirmLoading = false;
            that.close();
          });
        } catch (e) {
          console.error('数据处理异常', e);
          that.$message.error('数据处理异常：' + e.message);
          that.confirmLoading = false;
        }
      },
      beforeFileUpload(file) {
        const MAX_FILE_SIZE_MB = 10;
        const VALID_TYPES = [
          // 文档类型
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
          'application/pdf', // pdf
          'text/plain', // txt
          // 图片类型
          'image/jpeg',
          'image/jpg', 
          'image/png',
          'image/gif'
        ];

        const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
        const isTypeValid = VALID_TYPES.includes(file.type);

        if (!isSizeValid) {
          this.$message.error('文件大小不能超过10MB');
          return false;
        }

        if (!isTypeValid) {
          this.$message.error('只允许上传图片文件（JPG、PNG、GIF）和文档文件（DOCX、PDF、TXT）');
          return false;
        }

        return true;
      },
      handleFileChange({ fileList }) {
        const MAX_FILE_SIZE_MB = 10;
        const VALID_TYPES = [
          // 文档类型
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
          'application/pdf', // pdf
          'text/plain', // txt
          // 图片类型
          'image/jpeg',
          'image/jpg',
          'image/png',
          'image/gif'
        ];

        this.fileUpload.fileList = fileList.filter(file => {
          const isSizeValid = file.size / 1024 / 1024 <= MAX_FILE_SIZE_MB;
          const isTypeValid = VALID_TYPES.includes(file.type);
          return isSizeValid && isTypeValid;
        }).map(file => {
          let url = file.url;
          if (!url && file.response && file.response.success) {
            url = file.response.result;
          }
          return {
            name: file.name,
            url,
            type: file.type,
            size: file.size,
            status: file.status || 'done',
            uid: file.uid
          };
        });

        // 移除之前的文件类型和数量限制，允许上传多个文件
        this.model.attach = JSON.stringify(this.fileUpload.fileList);
      },
      handleCancel () {
        this.close();
      }
    }
  }
</script>

<style lang="less" scoped>

</style>
