-- 为餐厅表添加经纬度字段
ALTER TABLE `biz_restaurant` 
ADD COLUMN `longitude` DOUBLE NULL DEFAULT NULL COMMENT '经度' AFTER `region_address`,
ADD COLUMN `latitude` DOUBLE NULL DEFAULT NULL COMMENT '纬度' AFTER `longitude`;

-- 为酒店表添加经纬度字段  
ALTER TABLE `biz_hotel`
ADD COLUMN `longitude` DOUBLE NULL DEFAULT NULL COMMENT '经度' AFTER `region_address`,
ADD COLUMN `latitude` DOUBLE NULL DEFAULT NULL COMMENT '纬度' AFTER `longitude`;

-- 为常用语表添加经纬度字段
ALTER TABLE `biz_word`
ADD COLUMN `longitude` DOUBLE NULL DEFAULT NULL COMMENT '经度' AFTER `word_scence`,
ADD COLUMN `latitude` DOUBLE NULL DEFAULT NULL COMMENT '纬度' AFTER `longitude`;

-- 添加地图显示控制字段
ALTER TABLE `biz_trip_ai_content` 
ADD COLUMN `show_map` TINYINT(1) NULL DEFAULT '0' COMMENT '是否显示地图 0-不显示 1-显示' 
AFTER `show_contact`;

-- 添加定制师卡片开关和时间点开关字段
ALTER TABLE `biz_trip_ai_content` 
ADD COLUMN `show_designer` TINYINT(1) NULL DEFAULT '1' COMMENT '是否显示定制师卡片 0-不显示 1-显示' AFTER `full_screen`,
ADD COLUMN `show_time` TINYINT(1) NULL DEFAULT '1' COMMENT '是否显示时间点 0-不显示 1-显示' AFTER `show_schedule`;

-- 公司表新增最大账号数字段
ALTER TABLE `biz_company`
ADD COLUMN `max_account_num` INT NULL DEFAULT NULL COMMENT '最大账号数' AFTER `member_grade`;


-- 为公司表添加附件字段
ALTER TABLE `biz_company`
ADD COLUMN `attach` TEXT NULL COMMENT '附件' AFTER `wechat`;
