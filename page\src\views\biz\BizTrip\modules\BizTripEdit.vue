<template>
  <div>
    <a-spin :spinning="false" :class="{'pc-page':!mobile,'mobile-page':mobile}">
    <a-affix style="position: absolute;">
      <a-card class="my-aside" :bordered="false">
        <div :style="{ height: mobile?'100vh':'865px',overflowY:'auto',boxSizing:'border-box'}">
          <a-tabs default-active-key="scenicTab" tab-position="left" @change="onTabChange">
            <a-tab-pane key="scenicTab" tab="景区">
              <div>
                <!-- 地区筛选和系统景区组件 -->
                <region-filter 
                  v-model="scenic.selectedRegions" 
                  :system-scenic-data="systemScenicDataChecked"
                  @change="onScenicRegionChange"
                  @system-scenic-change="onSystemScenicChange"
                  style="margin-bottom: 10px;"
                />
                <div>
                  <div style="display: inline-block;" v-for="item in scenic.cityList" :key="`scenic-city-${item}`">
                    <a-checkable-tag :checked="scenic.selected.city==item"
                      @change="checked => onCityChange(item, checked,scenic)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <a-input-search :allowClear="true" style="margin-top:10px;margin-bottom:10px;width: 100%"
                  placeholder="输入景区名称或地区" v-model="scenic.search" @search="fetchScenicData()" />
                <div>
                  <div style="display: inline-block;" v-for="item in scenic.nameList" :key="`scenic-name-${item}`">
                    <a-checkable-tag class="fc-event"
                      :data-event='JSON.stringify({ type:"scenic",title:item,id:"scenic-"+item,extendedProps:scenic.nameMap[item]})'
                      :checked="scenic.selected.name==item" @change="checked => onNameChange(item, checked,scenic)">{{
                      item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <div style="width:100%;margin-top:10px;"
                  v-if="scenic.nameList !==undefined &&scenic.nameList != null && scenic.nameList.length > 0 ">
                  <!-- 子行程 支持#分隔符 -->
                  <template v-if="scenic.selected.data.tripDescArray!=null">
                    <div v-for="(item,key) in scenic.selected.data.tripDescArray"
                      :key="`scenic-tripDesc-${scenic.selected.name}-${key}`" :title="`${scenic.selected.name}-子行程`"
                      class="fc-event pannel-text-block-border"
                      :data-event='JSON.stringify({ type:"word",title:item,id:"scenic-tripDesc"+item+key,childTripDesc:true,extendedProps:scenic.selected.data})'>
                      {{ item }}
                    </div>
                  </template>
                  <!-- 景区名称 -->
                  <div :key="`scenic-name-${scenic.selected.name}`"
                    @click="addTripDesc('scenic-name', scenic.data,'#'+scenic.selected.name)"
                    :title="`${scenic.selected.name}-景区名称`" class="pannel-text-block">
                    #{{ scenic.selected.name }}
                  </div>
                  <!-- 景区短介绍 -->
                  <div :key="`scenic-advantageDesc-${scenic.selected.name}`"
                    @click="addTripDesc('scenic-advantageDesc', scenic.data,scenic.selected.data.advantageDesc)"
                    :title="`${scenic.selected.name}-景区特色`" class="pannel-text-block">
                    {{ scenic.selected.data.advantageDesc }}
                  </div>

                  <!-- 景区介绍 支持#分隔符 -->
                  <div v-for="(item,key) in scenic.selected.data.scenicDescArray"
                    :key="`scenic-scenicDesc-${scenic.selected.name}-${key}`"
                    @click="addTripDesc('scenic-scenicDesc', scenic.data,item)" :title="`${scenic.selected.name}-景区介绍`"
                    class="pannel-text-block">
                    {{ item }}
                  </div>

                  <!-- 景区图片 -->
                  <div v-for="(item,index) in scenic.selected.imgList"
                    :key="`scenic-img-${scenic.selected.name}-${index}`">
                    <img @click="addTripDesc('scenic-img', scenic.data,item.url)" :src="item.url"
                      :title="`${scenic.selected.name}-图片介绍`" class="pannel-img-block" />
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="hotelTab" tab="酒店">
              <div>
                <div>
                  <div style="display: inline-block;" v-for="item in hotel.cityList" :key="`hotel-city-${item}`">
                    <a-checkable-tag :checked="hotel.selected.city==item"
                      @change="checked => onCityChange(item, checked,hotel)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <!-- 星级筛选 -->
                <div style="margin-top:10px;background:#f5f5f5;padding:5px;border-radius:4px;">
                  <div style="display: inline-block;" v-for="item in hotel.starLevelList" :key="`hotel-star-${item}`">
                    <a-checkable-tag :checked="hotel.selected.star==item"
                      @change="checked => onStarChange(item, checked,hotel)" class="star-tag">
                      {{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <!-- 价格范围筛选 -->
                <div style="margin-top:5px;background:#f5f5f5;padding:5px;border-radius:4px;">
                  <div style="display: inline-block;" v-for="item in hotel.priceRangeList" :key="`hotel-price-${item}`">
                    <a-checkable-tag :checked="hotel.selected.priceRange==item"
                      @change="checked => onPriceRangeChange(item, checked,hotel)" class="price-tag">
                      {{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <a-input-search :allowClear="true" style="margin-top:10px;margin-bottom:10px;width: 100%"
                  placeholder="输入酒店名称或地区" v-model="hotel.search" @search="fetchHotelData()" />
                <div>
                  <div style="display: inline-block;" v-for="item in hotel.nameList" :key="`hotel-name-${item}`">
                    <a-checkable-tag :checked="hotel.selected.name==item" class="fc-event"
                      :data-event='JSON.stringify({ type:"hotel",title:item,id:"hotel-"+item,extendedProps:hotel.nameMap[item]})'
                      @change="checked => onNameChange(item, checked,hotel)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <div style="width:100%;margin-top:10px;"
                  v-if="hotel.nameList !==undefined &&hotel.nameList != null && hotel.nameList.length > 0 ">
                  <!-- 子行程 支持#分隔符 -->
                  <template v-if="hotel.selected.data.tripDescArray!=null">
                    <div v-for="(item,key) in hotel.selected.data.tripDescArray"
                      :key="`hotel-tripDesc-${hotel.selected.name}-${key}`" :title="`${hotel.selected.name}-子行程`"
                      class="fc-event pannel-text-block-border"
                      :data-event='JSON.stringify({ type:"word",title:item,id:"hotel-tripDesc"+item+key,childTripDesc:true,extendedProps:hotel.selected.data})'>
                      {{ item }}
                    </div>
                  </template>
                  <!-- 酒店名称 -->
                  <div :key="`hotel-name-${hotel.selected.name}`"
                    @click="addTripDesc('hotel-name', hotel.data,'#'+hotel.selected.name)"
                    :title="`${hotel.selected.name}-酒店名称`" class="pannel-text-block">
                    #{{ hotel.selected.name }}
                  </div>
                  <!-- 酒店短介绍 -->
                  <div :key="`hotel-advantageDesc-${hotel.selected.name}`"
                    @click="addTripDesc('hotel-advantageDesc', hotel.data,hotel.selected.data.advantageDesc)"
                    :title="`${hotel.selected.name}-酒店特色`" class="pannel-text-block">
                    {{ hotel.selected.data.advantageDesc }}
                  </div>
                  <!-- 酒店介绍  支持#分隔符 -->
                  <div v-for="(item,key) in hotel.selected.data.hotelDescArray"
                    :key="`hotel-hotelDesc-${hotel.selected.name}-${key}`"
                    @click="addTripDesc('hotel-hotelDesc', hotel.data,item)" :title="`${hotel.selected.name}-酒店介绍`"
                    class="pannel-text-block">
                    {{ item }}
                  </div>

                  <!-- 酒店图片 -->
                  <div v-for="(item,index) in hotel.selected.imgList"
                    :key="`hotel-img-${hotel.selected.name}-${index}`">
                    <img @click="addTripDesc('hotel-img', hotel.data,item.url)" :src="item.url"
                      :title="`${hotel.selected.name}-图片介绍`" class="pannel-img-block" />
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="trafficTab" tab="交通">
              <div>
                <div>
                  <div style="display: inline-block;" v-for="item in traffic.cityList" :key="`traffic-city-${item}`">
                    <a-checkable-tag :checked="traffic.selected.city==item"
                      @change="checked => onCityChange(item, checked,traffic)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <!-- 交通常用语 -->
                <div v-show="traffic.selected.city=='常用语'">
                  交通常用语
                </div>
                <!-- 交通费用 -->
                <div v-show="traffic.selected.city=='交通费用'">
                  <a-input-search :allowClear="true" style="margin-top:10px;margin-bottom:10px;width: 100%"
                    placeholder="输入交通名称" v-model="traffic.search" @search="fetchTrafficData()" />
                  <div>
                    <div style="display: inline-block;" v-for="item in traffic.nameList" :key="`traffic-name-${item}`">
                      <a-checkable-tag class="fc-event"
                        :data-event='JSON.stringify({type:"traffic", title:item,id:"traffic-"+item,extendedProps:traffic.nameMap[item]})'
                        :checked="traffic.selected.name==item"
                        @change="checked => onNameChange(item, checked,traffic)">{{ item }}
                      </a-checkable-tag>
                    </div>
                  </div>
                  <div style="width:100%;margin-top:10px;"
                    v-if="traffic.nameList !==undefined &&traffic.nameList != null && traffic.nameList.length > 0 ">

                    <!-- 交通介绍 -->
                    <div :key="`traffic-advantageDesc-${traffic.selected.name}`"
                      @click="addTripDesc('traffic-advantageDesc', scenic.data,scenic.selected.data.advantageDesc)"
                      :title="`${traffic.selected.name}-交通介绍`" class="pannel-text-block">
                      {{ traffic.selected.data.advantageDesc }}
                    </div>
                    <!-- 价格介绍 -->
                    <div v-if="traffic.selected.data.trafficType=='汽车'" :key="`traffic-price-${traffic.selected.name}`"
                      :title="`${traffic.selected.name}-汽车价格`" class="pannel-text-block">
                      <a-table size="small" :columns="trafficColumns"
                        :data-source="JSON.parse(traffic.selected.data.priceRule||'[]')" bordered
                        :pagination="false"></a-table>
                    </div>
                    <div v-if="traffic.selected.data.trafficType=='高铁动车'"
                      :key="`traffic-price-${traffic.selected.name}`" :title="`${traffic.selected.name}-高铁动车价格`"
                      class="pannel-text-block">
                      {{ traffic.selected.data.price }}
                    </div>
                    <div v-if="traffic.selected.data.trafficType=='其他'" :key="`traffic-price-${traffic.selected.name}`"
                      :title="`${traffic.selected.name}-其他价格`" class="pannel-text-block">
                      {{ traffic.selected.data.price }}
                    </div>
                  </div>
                </div>

              </div>
            </a-tab-pane>
            <a-tab-pane key="restTab" tab="餐厅">
              <div>
                <div>
                  <div style="display: inline-block;" v-for="item in rest.cityList" :key="`rest-city-${item}`">
                    <a-checkable-tag :checked="rest.selected.city==item"
                      @change="checked => onCityChange(item, checked,rest)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <a-input-search :allowClear="true" style="margin-top:10px;margin-bottom:10px;width: 100%"
                  placeholder="输入餐厅名称或地区" v-model="rest.search" @search="fetchRestData()" />
                <div>
                  <div style="display: inline-block;" v-for="item in rest.nameList" :key="`rest-name-${item}`">
                    <a-checkable-tag :checked="rest.selected.name==item" class="fc-event"
                      :data-event='JSON.stringify({type:"rest", title:item,id:"rest-"+item,extendedProps:rest.nameMap[item]})'
                      @change="checked => onNameChange(item, checked,rest)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <div style="width:100%;margin-top:10px;"
                  v-if="rest.nameList !==undefined &&rest.nameList != null && rest.nameList.length > 0 ">
                  <!-- 子行程 支持#分隔符 -->
                  <template v-if="rest.selected.data.tripDescArray!=null">
                    <div v-for="(item,key) in rest.selected.data.tripDescArray"
                      :key="`rest-tripDesc-${rest.selected.name}-${key}`" :title="`${rest.selected.name}-子行程`"
                      class="fc-event pannel-text-block-border"
                      :data-event='JSON.stringify({ type:"word",title:item,id:"rest-tripDesc"+item+key,childTripDesc:true,extendedProps:rest.selected.data})'>
                      {{ item }}
                    </div>
                  </template>
                  <!-- 餐厅名称 -->
                  <div :key="`rest-name-${rest.selected.name}`"
                    @click="addTripDesc('rest-name', rest.data,'#'+rest.selected.name)"
                    :title="`${rest.selected.name}-餐厅名称`" class="pannel-text-block">
                    #{{ rest.selected.name }}
                  </div>
                  <!-- 餐厅特色 -->
                  <div :key="`rest-advantageDesc-${rest.selected.name}`"
                    @click="addTripDesc('rest-advantageDesc', rest.data,rest.selected.data.advantageDesc)"
                    :title="`${rest.selected.name}-餐厅特色`" class="pannel-text-block">
                    {{ rest.selected.data.advantageDesc }}
                  </div>
                  <!-- 餐厅介绍 支持#分隔符 -->
                  <div v-for="(item,key) in rest.selected.data.restDescArray"
                    :key="`rest-restDesc-${rest.selected.name}-${key}`"
                    @click="addTripDesc('rest-restDesc', rest.data,item)" :title="`${rest.selected.name}-餐厅介绍`"
                    class="pannel-text-block">
                    {{ item }}
                  </div>

                  <!-- 餐厅图片 -->
                  <div v-for="(item,index) in rest.selected.imgList" :key="`rest-img-${rest.selected.name}-${index}`">
                    <img @click="addTripDesc('rest-img', rest.data,item.url)" :src="item.url"
                      :title="`${rest.selected.name}-图片介绍`" class="pannel-img-block" />
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="commonFeeTab" tab="通用">
              <div>
                <div>
                  <a-input-search :allowClear="true" style="margin-top:10px;margin-bottom:10px;width: 100%"
                    placeholder="输入通用费用名称或内容" v-model="commonFee.search" @search="fetchCommonFeeData()" />
                  <div>
                    <div style="display: inline-block;" v-for="(item, index) in commonFee.commonFeeList"
                      :key="`commonFee-name-${item.id || index}`">
                      <a-checkable-tag class="fc-event" :checked="commonFee.selected.name==item.feeTitle"
                        :data-event='JSON.stringify({ type:"commonFee",title:item.feeTitle,id:"commonFee-"+item.feeTitle,extendedProps:item})'
                        @change="checked => onCommonFeeChange(item, checked,commonFee)">{{ item.feeTitle }}
                      </a-checkable-tag>
                    </div>
                  </div>
                  <div style="width:100%;margin-top:10px;">
                    <!-- 费用详情内容 -->
                    <div v-if="commonFee.selected.name" :key="`commonFee-feedesc-${commonFee.selected.name}`"
                      @click="addTripDesc('commonFee-feeTitle', commonFee.selected.data,commonFee.selected.data.feeTitle)"
                      :title="`${commonFee.selected.name}-费用详情`" class="pannel-text-block">
                      {{ commonFee.selected.data.feeUnit + commonFee.selected.data.price }}
                    </div>
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="wordTab" tab="常用语">
              <div>
                <div>
                  <div style="display: inline-block;" v-for="item in word.scenceList" :key="`word-city-${item}`">
                    <a-checkable-tag :checked="word.selected.scence==item"
                      @change="checked => onCityChange(item, checked,word)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <a-input-search :allowClear="true" style="margin-top:10px;margin-bottom:10px;width: 100%"
                  placeholder="输入常用语名称或内容" v-model="word.search" @search="fetchWordData()" />
                <div>
                  <div style="display: inline-block;" v-for="item in word.nameList" :key="`word-name-${item}`">
                    <a-checkable-tag :checked="word.selected.name==item" class="fc-event"
                      :data-event='JSON.stringify({ type:"word",title:item,id:"word-"+item,extendedProps:word.nameMap[item]})'
                      @change="checked => onNameChange(item, checked,word)">{{ item }}
                    </a-checkable-tag>
                  </div>
                </div>
                <div style="width:100%;margin-top:10px;"
                  v-if="word.nameList !==undefined &&word.nameList != null && word.nameList.length > 0 ">
                  <!-- 常用语内容 -->
                  <div :key="`word-wordDesc-${word.selected.name}`"
                    @click="addTripDesc('word-wordDesc', word.data,word.selected.data.wordDesc)"
                    :title="`${word.selected.name}-常用语内容`" class="pannel-text-block">
                    {{ word.selected.data.wordDesc }}
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
          <div v-show="mobile" style="width: 100%;height:50px;"></div>
        </div>
      </a-card>
    </a-affix>
    <!-- 行程表单 -->
    <a-card class="my-trip-content" :bordered="false">
      <div v-show="!mobile" class="step-progress" :style="{ width: dynamicWidth }">
        <a-steps :current="step" labelPlacement="vertical" size="small" class="custom-steps" @change="onChange">
          <!-- 标准模式步骤 -->
          <template v-if="editMode === 'standard'">
            <a-step>
              <template slot="title">
                <span class="step-title">日程</span>
              </template>
            </a-step>
            <a-step>
              <template slot="title">
                <span class="step-title">介绍</span>
              </template>
            </a-step>
            <a-step>
              <template slot="title">
                <span class="step-title">报价</span>
              </template>
            </a-step>
          </template>

          <!-- 其他模式步骤 -->
          <template v-if="editMode === 'export' || editMode === 'h5'">
            <a-step>
              <template slot="title">
                <span class="step-title">日程</span>
              </template>
            </a-step>
            <a-step>
              <template slot="title">
                <span class="step-title">{{ editMode === 'export' ? '导出' : '发布' }}</span>
              </template>
            </a-step>
          </template>
        </a-steps>
      </div>
      <a-form layout="horizontal" class="form">
        <!-- 第一步 -->
        <div v-show="step==0">
          <a-form-item class="schedule-table" :style="{ width: dynamicWidth, 'margin-top': '20px' }">
            <a-tooltip placement="topLeft" title="添加行程天数" arrow-point-at-center>
              <a-button type="primary" @click="updateScheduleDay('add')" icon="plus" style="margin-left: 0px"
                v-show="calendarOptions.resources.length<MAX_SCHEDULE_DAYS"></a-button>
            </a-tooltip>
            <a-tooltip placement="topLeft" title="减少行程天数" arrow-point-at-center>
              <a-button type="primary" @click="updateScheduleDay('sub')" icon="minus" style="margin-left: 4px"
                v-show="calendarOptions.resources.length>1"></a-button>
            </a-tooltip>
            <a-tooltip placement="topLeft" title="管理行程天数" arrow-point-at-center>
              <a-button type="primary" @click="showDayManager()" icon="bars" style="margin-left: 4px"></a-button>
            </a-tooltip>
            <a-tooltip placement="topLeft" title="设置正餐最低餐标" arrow-point-at-center>
              <a-dropdown>
                <a-button type="dashed" shape="round" size="small" style="margin-left: 8px">
                  餐标 <a-icon type="down" />
                </a-button>
                <a-menu slot="overlay">
                  <a-menu-item @click="setMinRestPrice('350')">
                    <span>350</span>
                  </a-menu-item>
                  <a-menu-item @click="setMinRestPrice('400')">
                    <span>400</span>
                  </a-menu-item>
                  <a-menu-item @click="setMinRestPrice('500')">
                    <span>500</span>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </a-tooltip>
            <a-tooltip placement="topLeft" title="展开/折叠时间表" arrow-point-at-center>
              <a-button type="dashed" shape="round" size="small" @click="foldTimeline()" icon="arrows-alt"
                style="margin-left: 4px"></a-button>
            </a-tooltip>
            <a-tooltip v-if="editMode === 'standard'" placement="topLeft" :title="model.type === 1 ? '切换为散拼行程':'切换为团建行程'" arrow-point-at-center>
              <a-button type="dashed" shape="round" size="small" @click="switchType()" icon="swap"
                style="margin-left: 4px">
                {{ model.type === 1 ? '团' : '散' }}
              </a-button>
            </a-tooltip>
            <!-- 添加模式切换按钮 -->
            <a-tooltip placement="topLeft" title="选择编辑模式" arrow-point-at-center>
              <a-dropdown>
                <a-button type="dashed" shape="round" size="small" style="margin-left: 8px; background: #e6f7ff; border: 1px dashed #91d5ff; color: #1890ff; box-shadow: 0 2px 0 rgba(24,144,255,0.1);">
                  {{ getModeLabel }} <a-icon type="down" />
                </a-button>
                <a-menu slot="overlay" @click="handleModeChange">
                  <a-menu-item v-for="item in availableModeOptions" :key="item.value">
                    <a-icon v-if="editMode === item.value" type="check" />
                    {{ item.label }}
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </a-tooltip>
            <!-- 添加导入按钮 -->
            <a-tooltip placement="topLeft" title="AI智能导入行程" arrow-point-at-center>
              <a-button type="dashed" shape="round" size="small" @click="openImportModal" 
                class="ai-import-btn" style="margin-left: 8px;">
                <a-icon type="robot" style="margin-right: 3px;" class="ai-robot-icon" />
                <span>AI导入</span>
              </a-button>
            </a-tooltip>
            <!-- 添加地图按钮 -->
            <a-tooltip placement="topLeft" title="在地图上查看行程路线" arrow-point-at-center>
              <a-button type="dashed" shape="round" size="small" @click="openTripMapModal" 
                style="margin-left: 4px;">
                <a-icon type="environment" style="margin-right: 3px;" />
                <span>地图</span>
              </a-button>
            </a-tooltip>
            <FullCalendar ref="fullCalendar" :options="calendarOptions" />
          </a-form-item>
        </div>
        <!-- 第二步 -->
        <div v-show="step==1">
          <div style="height:20px;"></div>
          <a-form-item label="行程信息" class="my-form-item" style="position:relative;height:250px;">
            <div style="height:10px;width:100%;"></div>
            <div class="clearfix" style="position:absolute;">
              <a-upload name="file" accept=".png, .jpg, .jpeg" :action="uploadUrl" list-type="picture-card"
                :file-list="upload.fileList" :before-upload="beforeUpload" class="upload-img" @preview="onPreview"
                @change="handleChange">
                <div v-if="upload.fileList.length < 1">
                  <a-icon type="plus" />
                  <div class="ant-upload-text">
                    上传
                  </div>
                </div>
              </a-upload>
            </div>
            <a-input style="position:absolute;left:210px;width:290px;" v-model="model.tripName" placeholder="行程简称" />
            <a-textarea
              style="position:absolute;left:210px;top:50px;width:290px !important;max-width:inherit !important;height:205px;"
              v-model="model.tripFullName" placeholder="行程完整名称" />
            <div style="position:relative;top:210px;display:flex;align-items:center;">
              <a-input style="width:80px;margin-right:5px;" v-model="model.author" placeholder="作者" />
              <a-input-number style="width:80px;margin-right:10px;" placeholder="推荐度" v-model="model.searchIndex" />
            </div>
            <a-modal :visible="showImgList" title="点击选择封面图片" width="800px" @cancel="showImgList = false" :footer="null">
              <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div v-for="(image, index) in coverImgList" :key="index" @click="selectImage(image)"
                  style="cursor: pointer; width: calc(20% - 10px); height: 100px; overflow: hidden; position: relative;">
                  <img :src="image" alt="" style="width: 100%; height: 100%; object-fit: cover;">
                </div>
              </div>
            </a-modal>
            <!-- 修改导入模态框 -->
            <a-modal 
              title="AI智能导入行程"
              :visible="importModalVisible"
              @ok="handleImportOk"
              @cancel="handleImportCancel"
              width="800px"
              :confirmLoading="importLoading"
              okText="确定"
              cancelText="取消"
              class="ai-import-modal"
              :maskStyle="{background: 'rgba(0, 0, 0, 0.65)'}"
            >
              <div class="ai-modal-header">
                <div class="ai-modal-header-line"></div>
                <div class="ai-modal-header-text">Gemini|DeepSeek|Qwen|ChatGPT驱动</div>
                <div class="ai-modal-header-line"></div>
              </div>

              <a-tabs :activeKey="importType" @change="handleImportTypeChange" class="ai-tabs">
                <a-tab-pane key="chatImport" tab="聊天">
                  <a-form-item>
                    <a-textarea 
                      placeholder="描述您想要的行程，例如：帮我设计一个深圳出发的6天的云南旅行，要包括玉龙雪山..."
                      :rows="6"
                      v-model="importPrompt"
                      class="ai-textarea"
                    />
                  </a-form-item>
                </a-tab-pane>

                <a-tab-pane key="formImport" tab="填空">
                  <a-form layout="vertical">
                    <a-form-item label="行程天数">
                      <a-input-number v-model="formImport.days" :min="1" :max="MAX_SCHEDULE_DAYS" placeholder="天数" class="ai-input-number" />
                    </a-form-item>
                    <a-form-item label="出发地">
                      <a-input v-model="formImport.departure" placeholder="例如：深圳" class="ai-input" />
                    </a-form-item>
                    <a-form-item label="目的地">
                      <a-input v-model="formImport.destination" placeholder="例如：云南" class="ai-input" />
                    </a-form-item>
                    <a-form-item label="最看重的景点">
                      <a-input v-model="formImport.preferredScenic" placeholder="例如：玉龙雪山 丽江古城。可多个景点" class="ai-input" />
                    </a-form-item>

                    <!-- 聊天框作为补充信息 -->
                    <a-form-item label="额外提示">
                      <a-textarea 
                        placeholder="您可以补充额外的要求或说明，如：请在行程中加入更多自由活动时间..."
                        :rows="3"
                        v-model="importPrompt"
                        class="ai-textarea"
                      />
                    </a-form-item>
                  </a-form>
                </a-tab-pane>

                <a-tab-pane key="fileImport" tab="文件">
                  <a-form-item label="文档">
                    <a-upload
                      name="file"
                      :fileList="importFileList"
                      :beforeUpload="beforeImportUpload"
                      :remove="() => { importFileList = []; return true; }"
                      class="ai-upload"
                    >
                      <a-button>
                        <a-icon type="upload" /> 选择文件
                      </a-button>
                      <span style="margin-left: 8px; color: rgba(0,0,0,.45);">支持格式：Word、PDF文件</span>
                    </a-upload>
                  </a-form-item>

                  <!-- 聊天框作为补充信息 -->
                  <a-form-item label="额外提示">
                    <a-textarea 
                      placeholder="您可以补充额外的要求或说明，如：请在行程中加入更多自由活动时间、请删除第三天"
                      :rows="3"
                      v-model="importPrompt"
                      class="ai-textarea"
                    />
                  </a-form-item>
                </a-tab-pane>

                <a-tab-pane key="urlImport" tab="网址">
                  <a-form-item label="网址">
                    <a-input 
                      placeholder="行程网址" 
                      v-model="importUrl"
                      allowClear
                      class="ai-input"
                    />
                  </a-form-item>

                  <!-- 添加警告提示 -->
                  <div style="margin: 8px 0 16px; padding: 8px 12px; background-color: #fffbe6; border: 1px solid #ffe58f; border-radius: 4px;">
                    <a-icon type="warning" style="color: #faad14; margin-right: 8px;" />
                    <span style="color: #555;">携程等网址有反AI爬虫策略，导致内容无法被正确分析，建议复制行程部分文字到 聊天 进行导入</span>
                  </div>

                  <!-- 聊天框作为补充信息 -->
                  <a-form-item label="额外提示">
                    <a-textarea 
                      placeholder="您可以补充额外的要求或说明"
                      :rows="3"
                      v-model="importPrompt"
                      class="ai-textarea"
                    />
                  </a-form-item>
                </a-tab-pane>

                <a-tab-pane key="voiceImport" tab="语音">
                  <div style="text-align: center; padding: 40px;">
                    <a-icon type="audio" style="font-size: 48px; color: #d9d9d9;" class="ai-voice-icon" />
                    <p style="margin-top: 16px;">语音导入功能开发中，敬请期待...</p>
                  </div>
                </a-tab-pane>
              </a-tabs>

              <!-- AI思考动画，在importLoading为true时显示 -->
              <ai-thinking-animation v-if="importLoading" />
            </a-modal>
          </a-form-item>
          <a-form-item label="行程介绍" class="my-form-item">
            <vue-ueditor-wrap style="line-height: 20px !important;" :config="config" @ready="ueditorReady"
              @before-init="beforeReady" v-model="model.tripDesc" editor-id="ueEditor"></vue-ueditor-wrap>
          </a-form-item>
          <a-form-item label="状态" class="my-form-item">
            <j-dict-select-tag style="width:100px;" v-model="model.status" placeholder="状态"
              dictCode="biz_trip_status" />
          </a-form-item>
        </div>
        <!-- 第三步/正常行程 -->
        <div v-show="step===2 && model.type !== 2">
          <!-- 景区价格标签 -->
          <a-form-item label="景区" class="my-form-item">
            <a-tag v-for="(tag, index) in priceTag.scenic" :closable="true"
              @close="() => onPriceTagClose('scenic',tag.title,tag.resourceId)"
              :key="`tag-price-scenic-${tag.title}-${tag.resourceId}-${index}`">
              {{ tag.title }}
            </a-tag>
          </a-form-item>
          <a-form-item v-if="priceTag.excludeScenic.length>=1" label="不计费景点" class="my-form-item"
            style="text-decoration:line-through">
            <a-tag v-for="(tag, index) in priceTag.excludeScenic" style="text-decoration:line-through" :closable="true"
              @close="() => onPriceTagClose('excludeScenic',tag.title,tag.resourceId)"
              :key="`tag-price-excludeScenic-${tag.title}-${tag.resourceId}-${index}`">
              {{ tag.title }}
            </a-tag>
          </a-form-item>
          <!-- 酒店价格标签 -->
          <a-form-item label="酒店" class="my-form-item">
            <div v-if="model.dayNum>1">
              <div v-for="day in model.dayNum-1" :key="day">
                <span v-if="model.dayNum>2">第{{ day }}天：</span>
                <a-tag v-for="(tag, index) in priceTag.hotel.filter(item=>item.resourceId==day)"
                  :key="`tag-price-hotel-${tag.title}-${tag.resourceId}-${index}`" :closable="true"
                  @close="() => onPriceTagClose('hotel',tag.title,tag.resourceId)">
                  {{ tag.title }}
                </a-tag>
              </div>
            </div>
            <div v-if="model.dayNum==1">
              <a-tag v-for="(tag, index) in priceTag.hotel.filter(item=>item.resourceId=='1')"
                :key="`tag-price-hotel-${tag.title}-${tag.resourceId}-${index}`" :closable="true"
                @close="() => onPriceTagClose('hotel',tag.title,tag.resourceId)">
                {{ tag.title }}
              </a-tag>
            </div>
          </a-form-item>
          <!-- 餐厅价格标签 -->
          <a-form-item label="餐厅" class="my-form-item">
            <a-tag v-for="(tag, index) in priceTag.rest" :key="`tag-price-rest-${tag.title}-${tag.resourceId}-${index}`"
              :closable="true" @close="() => onPriceTagClose('rest',tag.title,tag.resourceId)">
              {{ "D" + tag.resourceId + "-" + tag.title }}
            </a-tag>
          </a-form-item>
          <a-form-item label="交通" class="my-form-item">
            <a-tag v-for="(tag, index) in priceTag.traffic" :key="`tag-price-traffic-${tag.title}-${tag.resourceId}-${index}`"
              :closable="true" @close="() => onPriceTagClose('traffic',tag.title,tag.resourceId)">
              {{ tag.title }}
            </a-tag>
          </a-form-item>
          <a-form-item label="通用成本" class="my-form-item">
            <a-tag v-for="(tag, index) in priceTag.commonFee" :key="`tag-price-commonFee-${tag.title}-${tag.resourceId}-${index}`"
              :closable="true" @close="() => onPriceTagClose('commonFee',tag.title,tag.resourceId)">
              {{ tag.title }}
            </a-tag>
          </a-form-item>
          <a-form-item label="自定义" class="my-form-item">
            <a-tag v-for="(tag, index) in priceTag.cust" :key="`tag-price-cust-${tag.title}-${tag.resourceId}-${index}`" :closable="true"
              @close="() => onPriceTagClose('cust',tag.title,tag.resourceId)">
              {{ tag.title }}
            </a-tag>
            <!-- 添加自定义标签和价格 -->
            <a-tag style="background: #fff;" @click="openPriceModal('cust')">
              <a-icon type="plus" />
            </a-tag>
          </a-form-item>
          <a-divider dashed style="width:390px;" />
          <a-form-item label="报价提醒" class="my-form-item">
            <a-input style="position:absolute;left:10px;width:20rem;" v-model="model.shopAutoPriceRemark"
              placeholder="店铺自动报价页面提醒" />
          </a-form-item>
          <a-form-item label="自动报价" class="my-form-item">
            <a-switch checked-children="开" un-checked-children="关" default-checked v-model="model.autoPrice" />
          </a-form-item>
          <a-form-item label="自动刷新-行程介绍" class="my-form-item">
            <a-switch checked-children="开" un-checked-children="关" default-checked v-model="model.autoRefreshBoolean" />
          </a-form-item>
        </div>
        <!-- 第三步/散拼行程 -->
        <div v-show="step === 2 && model.type === 2" class="scattered-trip-box">
          <div>
            <div class="scattered-trip-title">团期</div>
            <div class="date-box" v-for="(period, index) in model.tripPeriodList" :key="`period-${index}`">
              <a-date-picker valueFormat='YYYY-MM-DD HH:mm:ss' class="date-box-item" v-model="period.tripDate"
                :disabled="!!model.spPeriodDesc" />
              <j-dict-select-tag class="date-box-item" v-model="period.status" placeholder="状态"
                dictCode="biz_trip_period_status" :disabled="!!model.spPeriodDesc" />
              <a-input v-model='period.remark' placeholder="备注说明" class="date-box-item"
                :disabled="!!model.spPeriodDesc" />

              <a-button icon="plus" type="primary" shape="circle" ghost size="small" @click="plusPeriod()"
                v-if="index === model.tripPeriodList.length - 1" :disabled="!!model.spPeriodDesc" />
              <a-button icon="minus" shape="circle" type="danger" ghost size="small" v-else @click="minusPeriod(index)"
                :disabled="!!model.spPeriodDesc" />
            </div>
            <!-- 团期描述输入框 -->
            <a-textarea v-model="model.spPeriodDesc" placeholder="团期描述，例如:天天发团（如果填写，将覆盖上方团期设置，且无法展示成团状态）"
              :auto-size="{ minRows: 1, maxRows: 3 }" />

            <div class="box-line"></div>
            <div class="scattered-trip-title">费用</div>
            <div>
              <span style="margin-left: 10px;">成人：</span>
              <a-input type="number" :suffix="model.priceRuleMap.currency || defaultCurrency" placeholder="成人价格"
                v-model='model.priceRuleMap.adult' class="price-item" />

              <span style="margin-left: 10px;">儿童：</span>
              <a-input type="number" :suffix="model.priceRuleMap.currency || defaultCurrency" placeholder="儿童价格"
                v-model='model.priceRuleMap.children' class="price-item" />

              <span style="margin-right: 5px;">单位：</span>
              <a-input v-model="model.priceRuleMap.currency" :placeholder="defaultCurrency" class="price-item"
                style="width: 80px;" />

            </div>

            <a-textarea style="margin-top: 20px" v-model="model.priceRemark" class="price-textarea" placeholder="费用备注"
              :auto-size="{ minRows: 3, maxRows: 15 }" />

            <!-- 新增自费项目部分 -->
            <div class="box-line"></div>
            <div class="scattered-trip-title">
              <a-input v-model="model.optionalItemsTitle" :style="{ width: '100px', marginRight: '10px' }"
                :placeholder="this.defaultOptionalItemsTitle" />
            </div>
            <a-textarea v-model="model.optionalItems" class="price-textarea"
              :placeholder="`请输入${model.optionalItemsTitle || this.defaultOptionalItemsTitle}信息`"
              :auto-size="{ minRows: 3, maxRows: 15 }" />

            <div class="box-line"></div>
            <div class="scattered-trip-title">集合点</div>
            <a-textarea v-model="model.spPlace" class="price-textarea" placeholder="集合点"
              :auto-size="{ minRows: 3, maxRows: 15 }" />
            <div class="box-line"></div>
            <div class="scattered-trip-title">温馨提示</div>
            <a-textarea v-model="model.tripTip" class="price-textarea" placeholder="温馨提示"
              :auto-size="{ minRows: 3, maxRows: 15 }" />
            <div class="attach-box">
              附件：
              <a-upload name="file" :action="uploadUrl" :fileList="fileUpload.fileList"
                :beforeUpload="beforeFileUpload" @change="handleFileChange">
                <a-button>
                  <a-icon type="upload" />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </div>
        </div>
        <a-form-item>
          <button type="button" v-show="step<2" class="ant-btn" style="margin-right:10px;"
            @click="stepChange('next')"><span>下一步</span></button>
          <button type="button" v-show="step>0" class="ant-btn" style="margin-right:10px;"
            @click="stepChange('prev')"><span>上一步</span></button>
          <button type="button" class="ant-btn ant-btn-primary" style="margin-right:10px;"
            @click="handleSave">保 存
          </button>
          <button type="button" class="ant-btn ant-btn-danger" style="margin-right:10px;" @click="this.closeCurrent"><span>关 闭</span>
          </button>
        </a-form-item>
      </a-form>
      <set-cust-price ref="setCustPrice" @afterSetPrice="afterSetPrice"></set-cust-price>
      <quick-export-modal ref="quickExportModal" @ok="handleQuickExportOk" />
      <a-modal v-model="schedulePrompt.show" title="添加日程" ok-text="确认" cancel-text="取消" @ok="onSchedulePromptChange"
        @calcel="()=>{schedulePrompt.word='';schedulePrompt.event={}}">
        <p>{{ schedulePrompt.tip }}</p>
        <a-input v-model="schedulePrompt.word" placeholder="活动描述" />
        <a-row justify="space-between">
          <a-col>
            <a-button @click="addCustItem(1)">自由活动</a-button>
            <a-button @click="addCustItem(2)">返回酒店</a-button>
            <a-button @click="addCustItem(3)">前往入住</a-button>
            <a-button @click="addCustItem(4)">早到酒店</a-button>
            <a-button @click="addCustItem(5)">自由温泉</a-button>
          </a-col>
        </a-row>
      </a-modal>
       <!-- 编辑内容模态框 -->
      <a-modal v-model="editModal.visible" :title="editModal.title" @ok="handleEditOk" @cancel="handleEditCancel">
        <a-textarea v-model="editingContent" :auto-size="{ minRows: 3, maxRows: 6 }" placeholder="请输入行程内容" />
      </a-modal>
      <export-word-modal ref="exportWordModal" />
      <!-- 添加行程天数管理模态框 -->
      <a-modal v-model="dayManager.visible" title="行程天数管理" width="600px" @ok="dayManager.visible = false">
        <div class="day-manager-container">
          <draggable 
            v-model="calendarOptions.resources" 
            @start="handleDragStart"
            @end="handleDragEnd"
            :options="{animation: 150, handle: '.drag-handle'}"
            ghost-class="day-manager-item-ghost"
            chosen-class="day-manager-item-chosen">
            <div v-for="(element, index) in calendarOptions.resources" :key="index" class="day-manager-item">
              <span class="day-title">
                <a-icon type="drag" class="drag-handle" />
                {{ element.title }}
              </span>
              <div class="day-actions">
                <a-button type="primary" size="small" @click="insertDayBefore(index)"
                  :disabled="calendarOptions.resources.length >= MAX_SCHEDULE_DAYS">
                  在此前插入
                </a-button>
                <a-button type="primary" size="small" @click="insertDayAfter(index)"
                  :disabled="calendarOptions.resources.length >= MAX_SCHEDULE_DAYS">
                  在此后插入
                </a-button>
                <a-button type="danger" size="small" @click="removeDay(index)"
                  :disabled="calendarOptions.resources.length <= 1">
                  删除
                </a-button>
              </div>
            </div>
          </draggable>
        </div>
      </a-modal>
    </a-card>

    <!-- 添加事件菜单组件 -->
    <div v-if="eventMenu.visible" class="fc-event-dropdown" :style="eventMenuStyle" @click.stop>
      <a-menu class="event-action-menu">
        <a-menu-item key="delete" @click="handleEventAction('delete')">
          <a-icon type="delete" /> 删除日程
        </a-menu-item>
        <a-menu-item
          v-if="eventMenu.currentEvent && eventMenu.currentEvent.extendedProps && eventMenu.currentEvent.extendedProps.bizType === 'word'"
          key="edit" @click="handleEventAction('edit')">
          <a-icon type="edit" /> 编辑内容
        </a-menu-item>
        <a-menu-item key="hideTime" @click="handleEventAction('hideTime')">
          <a-icon type="eye-invisible" /> 隐藏时间
        </a-menu-item>
        <a-menu-item key="adjustTime" @click="handleEventAction('adjustTime')">
          <a-icon type="clock-circle" /> 微调时间
        </a-menu-item>
      </a-menu>
    </div>

    <!-- 保留原有的时间选择器模态框 -->
    <div v-if="timeModal.visible" class="time-modal-bg" @click.self="closeTimeModal">
      <div class="time-modal-content" :style="timeModalStyle">
        <div class="time-modal-title">设置显示时间</div>
        <div class="time-input-group">
          <div class="time-input-item">
            <label>开始时间:</label>
            <input type="time" v-model="timeModal.startValue" class="time-input" />
        </div>
          <div class="time-input-item" v-if="timeModal.hasEndTime">
            <label>结束时间:</label>
            <input type="time" v-model="timeModal.endValue" class="time-input" />
          </div>
        </div>
        <div class="button-container">
          <button class="cancel-btn" @click="closeTimeModal">取消</button>
          <button class="confirm-btn" @click="confirmTimeAdjust">确定</button>
        </div>
      </div>
    </div>
  </a-spin>
  
  <!-- 地图组件 - 移到 a-spin 外面，避免层级问题 -->
  <trip-map-modal ref="tripMapModal" />
</div>
</template>

<script>
import { getAction, deleteAction, putAction, postAction, httpAction, httpActionWithTimeout } from '@/api/manage'
import {RouterLinkMixinConfig} from '@/mixins/RouterLinkMixinConfig';
import { mixinDevice } from '@/utils/mixin.js';
import VueUeditorWrap from 'vue-ueditor-wrap'; // ES6 Module
import FullCalendar from '@fullcalendar/vue'
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import interactionPlugin, {Draggable} from '@fullcalendar/interaction';
import {axios} from '@/utils/request';
import SetCustPrice from './SetCustPrice.vue'
import QuickExportModal from './../modules/QuickExportModal'
import ExportWordModal from './ExportWordModal.vue'
import AiThinkingAnimation from './AiThinkingAnimation.vue'
import draggable from 'vuedraggable'
import RegionFilter from '@/components/RegionFilter'
import TripMapModal from './TripMapModal.vue'

import {
  commonRestBizIdArr,
  commonRestBreakfast,
  commonRestLunch300,
  commonRestLunch350,
  commonRestLunch400,
  commonRestLunch500,
  commonRestDinner300,
  commonRestDinner350,
  commonRestDinner400,
  commonRestDinner500,
  trafficTips,
  scenicTips,
  hotelTips,
  departTips,
  returnTips
} from './../setting/schedule'


export default {
  name: 'BizTripEdit',
  mixins: [RouterLinkMixinConfig, mixinDevice],
  inject: ['closeCurrent', 'onCloseCurrent'],
  components: {
    VueUeditorWrap, FullCalendar, SetCustPrice,QuickExportModal,ExportWordModal, AiThinkingAnimation,draggable, RegionFilter, TripMapModal
  },
  activated() {
    this.loading = false;
    this.fileUpload = { fileList: [] }
   
    // 检查路由参数ID和模型ID是否不一致
    if (this.$route.params.id && this.model.id && 
        this.$route.params.id !== null && this.$route.params.id !== undefined &&
        this.model.id !== null && this.model.id !== undefined &&
        this.$route.params.id !== this.model.id) {
      this.$message.error('请关闭老行程再编辑新行程！', 5);
      return;
    }

    const initializeComponent = () => {
      this.fetchPageData();
      if (!this.editing) {
        this.editing = true;
        if (this.$route.params.id) {
          this.model.id = this.$route.params.id;
          this.getEditData();
          this.isScheduleInitStatus = false;
        } else {
          this.isScheduleInitStatus = true;
          let lastScheduleDayNum = localStorage.getItem('lastScheduleDayNum');
          this.updateScheduleDay('set', lastScheduleDayNum || 2);
        }
      }
    };

    if (this.sysUser.roleCode.startsWith('shop')) {
      this.getDefaultComId()
        .then(() => {
          initializeComponent();
        })
        .catch(error => {
          console.error('获取默认公司ID失败', error);
          this.$message.error('获取默认公司ID失败，请重试');
        });
    } else {
      initializeComponent();
    }
  },
  data() {
    return {
      sysUser: {},
      editing: false,//正在编辑
      // 添加编辑模式相关配置
      editMode: 'export', // standard | export | h5 - 初始化为默认值，在created中根据用户设置
      //全部模式的选项
      modeOptions: [
        { label: '标准模式', value: 'standard' },
        { label: '快捷导出', value: 'export' },
        { label: '小程序H5', value: 'h5' }
      ],
      //可用的模式的选项
      availableModeOptions:  [
        { label: '标准模式', value: 'standard' },
        { label: '快捷导出', value: 'export' },
        { label: '小程序H5', value: 'h5' }
      ],
      timelineFoldStatus: true, //默认折叠起来时间,只显示7~21点
      systemScenicDataChecked: false,  // 系统景区数据勾选框是否选中
      showImgList: false,
      coverImgList: [],
      schedulePrompt: {
        show: false,
        tip: '',
        word: '',
        event: {}
      },
      isScheduleInitStatus: false, //日程表是否为初始状态,如果是，点击日程 + -才可以自动添加对应天数的event默认事件，否则不添加
      trafficColumns: [
        {
          title: '编号',
          dataIndex: 'id',
          key: 'id',
        },
        {
          title: '车型',
          dataIndex: 'carType',
          key: 'carType',
          ellipsis: true,
        },
        {
          title: '座位数',
          dataIndex: 'seat',
          key: 'seat',
          ellipsis: true,
        },
        {
          title: '车租',
          dataIndex: 'rent',
          key: 'rent',
          ellipsis: true,
        },
        {
          title: '路桥费',
          dataIndex: 'roadFee',
          key: 'roadFee',
          ellipsis: true,
        },
      ],
      calendarOptions: {
        // schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
        schedulerLicenseKey: 'GPL-My-Project-Is-Open-Source',
        // 引入的插件，比如fullcalendar/daygrid，fullcalendar/timegrid引入后才可显示月，周，日
        plugins: [interactionPlugin, resourceTimeGridPlugin],
        initialView: 'resourceTimeGridDay', // 默认为那个视图（月：dayGridMonth，周：timeGridWeek，日：timeGridDay）
        views: {},
        resources: [{id: '1', title: '第1天'}, {id: '2', title: '第2天'}],
        locale: 'zh-cn', // 切换语言，当前为中文
        eventColor: '#3BB2E3', // 全部日历日程背景色
        eventBorderColor: '#fff',
        themeSystem: 'bootstrap', // 主题色(本地测试未能生效)
        initialDate: '3000-01-01', // 自定义设置背景颜色时一定要初始化日期时间
        // aspectRatio: 2, // 设置日历单元格宽度与高度的比例。
        displayEventTime: false, // 是否显示时间
        allDaySlot: true, // 周，日视图时，all-day 不显示
        allDayText: "通用",
        slotLabelFormat: {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false // 设置时间为24小
        },
        slotMinTime: "07:00:00",//顶部时间
        allDayMaintainDuration: true,
        slotMaxTime: "21:00:00",//底部时间
        //scrollTime:"06:00:00",//默认滚动时间
        slotDuration: "00:30:00",//刻度
        defaultTimedEventDuration: "00:30:00",//事件默认时长
        eventClassNames: "schedule-cell",
        headerToolbar: {
          left: '',
          center: '',
          right: '',
        },
        // longPressDelay:1000,
        // eventLongPressDelay:5000,
        // selectLongPressDelay:5000,
        contentHeight: 'auto',
        droppable: true,
        rerenderDelay: 100,//初始化延迟时间ms
        // 事件
        drop: this.onDrop,//候选数据拖动到日程中触发
        // eventDrop: this.eventChange, // 日程内event拖动触发
        eventClick: this.clickEvent, // 点击日历日程事件
        dateClick: this.dateClick,
        eventChange: this.onInnerDrop, // 日程内拖动会触发
        // eventDidMount: this.eventDidMount, // 日程内事件安装提示事件
        // loading: this.loadingEvent, // 视图数据加载中、加载完成触发（用于配合显示/隐藏加载指示器。）
        editable: true, // 是否可以进行（拖动、缩放）修改
        selectable: false, // 是否可以选中日历格
        selectMirror: true,
        selectMinDistance: 0, // 选中日历格的最小距离
        slotEventOverlap: false, // 相同时间段的多个日程视觉上是否允许重叠，默认true允许
        resourceOrder: function(a, b) {
          return a.id - b.id;
        },
      },
      dragInit: null,
      calendarApi: null,
      scenic: {
        dataType: "scenic",
        cityList: null,
        cityMap: null,
        nameList: null,
        nameMap: {},
        search: null,
        selectedRegions: [], // 新增：选中的地区列表
        selected: {
          city: null,
          name: null,
          data: {},
          imgList: [],
        },
      },
      traffic: {
        dataType: "traffic",
        cityList: null,
        cityMap: null,
        nameList: null,
        nameMap: {},
        search: null,
        selected: {
          city: null,
          name: null,
          data: {},
          imgList: [],
        },
      },
      rest: {
        dataType: "rest",
        cityList: null,
        cityMap: null,
        nameList: null,
        nameMap: {},
        search: null,
        selected: {
          city: null,
          name: null,
          data: {},
          imgList: [],
        },
      },
      hotel: {
        dataType: "hotel",
        cityList: null,
        cityMap: null,
        nameList: null,
        nameMap: {},
        starLevelList: null,
        starMap: {},
        priceRangeList: null,
        priceRangeMap: {},
        search: null,
        selected: {
          city: null,
          name: null,
          star: null,
          priceRange: null,
          data: {},
          imgList: [],
        },
      },
      word: {
        dataType: "word",
        scenceList: null,
        scenceMap: null,
        nameList: null,
        nameMap: {},
        search: null,
        selected: {
          scence: null,
          name: null,
          data: {},
        },
      },
      commonFee: {
        dataType: "commonFee",
        commonFeeList: null,
        search: null,
        selected: {
          name: null,
          data: {},
        },
      },
      defaultCurrency: '元',
      defaultOptionalItemsTitle: '自费项目',    
      model: {
        id: null,
        tripDesc: '',
        advantageDesc: null,
        price: null,
        priceRule: null,
        priceRuleMap: {
          // 成人
          adult: null,
          //儿童
          children: null,
        },
        tripName: null,
        tripFullName: null,
        schedule: null,
        searchIndex: 0,
        status: "3",//定制行程
        tripTip: null,
        spPlace: null,
        spPeriodDesc: '',
        optionalItemsTitle: this.defaultOptionalItemsTitle,
        optionalItems: '',
        priceRemark: null,
        spAttach: null,
        imgUrl: null,
        dayNum: 1,
        autoPrice: false,
        shopAutoPriceRemark: null,//用于店铺自动报价页面提醒
        autoRefresh: 1,
        autoRefreshBoolean: true,
        type: 1,
        tripPeriodList:[{}],
      },
      resetModel: {
        id: null,
        tripDesc: '',
        advantageDesc: null,
        price: null,
        priceRule: null,
        priceRuleMap: {
          adult: null,
          children: null,
          currency: '元'
        },
        tripName: null,
        tripFullName: null,
        schedule: null,
        searchIndex: 0,
        status: "3",//定制行程
        tripTip: null,
        spPlace: null,
        spPeriodDesc:  null,
        optionalItemsTitle: this.defaultOptionalItemsTitle,
        optionalItems:  null,
        priceRemark: null,
        spAttach: null,
        imgUrl: null,
        dayNum: 1,
        autoPrice: false,
        shopAutoPriceRemark: null,
        autoRefresh: 1,
        autoRefreshBoolean: true,
        type: 1,
        tripPeriodList:[{}]
      },
      currentEditor: null,
      upload: {
        fileList: [],
        previewImage: null,
        previewVisible: false,
      },
      fileUpload: {
        fileList: [],
      },
      importFileList: [], // 导入文件列表
      loading: true,
      url: {
        add: "/biz/bizTrip/add",
        get: "/biz/bizTrip/queryById",
        edit: "/biz/bizTrip/edit",
        getRichText: "/biz/bizTrip/getRichText",
        getCandidateCoverImages: "/biz/bizTrip/getCandidateCoverImages",
        getDefaultComId: "/biz/bizTrip/getDefaultComId",
        getComanyInfo: "/biz/bizCompany/queryById",
        list: "/biz/bizTrip/list",
      },
      config: {
        //可以在此处定义工具栏的内容
        toolbars: [
          ['135editor',
            'undo', //撤销
            'redo', //重做
            'bold', //加粗
            'underline', //下划线
            'fontfamily', //字体
            'fontsize', //字号
            'justifyleft', //居左对齐
            'justifyright', //居右对齐
            'justifycenter', //居中对齐
            'insertvideo', //视频
            'formatmatch', //格式刷
            'source', //源代码
            'removeformat', //清除格式
            // 'preview', //预览
            'fullscreen', //全屏
          ]
        ],
        autoHeightEnabled: true, //是否自动长高
        autoFloatEnabled: true, //是否工具栏可浮动
        initialContent: '请输入内容', //初始化编辑器的内容,也可以通过textarea/script给值，看官网例子
        autoClearinitialContent: true, //是否自动清除编辑器初始内容，注意：如果focus属性设置为true,这个也为真，那么编辑器一上来就会触发导致初始化的内容看不到了
        initialFrameWidth: 500,
        initialFrameHeight: 400,
        focus: true,
        enableAutoSave: false, //不启用自动保存（有个烦人的自动保存成功弹窗有时候挡住按钮无法关闭）
        // saveInterval: 0, //自动保存间隔时间， 单位ms
        BaseUrl: '',
        UEDITOR_HOME_URL: (axios.defaults.baseURL || "") + '/UEditor/',
        serverUrl: 'https://www.woyaotuanjian.com/trip/file/cos',
      },
      step: 0,
      priceTag: {
        scenic: [],
        hotel: [],
        rest: [],
        traffic: [],
        commonFee: [],
        //自定义价格项
        cust: [],
        //景区不计费名单
        excludeScenic: [],
        //自动报价开关
        autoPrice: false
      },
      scenicIds: [],
      hotelIds: [],
      restIds: [],
      screenWidth: window.innerWidth,
      companyConfig: null,
      importModalVisible: false,
      importLoading: false,
      importUrl: '',
      importPrompt: '',
      // 初始化表单对象
      importForm: this.$form.createForm(this),
      dayManager: {
        visible: false,
        originalIds: null, // 用于存储拖拽前的天数ID
      },
      formImport: {
        days: '',
        departure: '',
        destination: '',
        preferredScenic: ''
      },
      importType: 'fileImport', // 默认导入类型
      
      // 事件菜单相关
      eventMenu: {
        visible: false,
        position: { x: 0, y: 0 },
        currentEvent: null
      },
      
      // 添加鼠标悬停状态
      hoveredItem: null,
      
      // 原有的时间模态框相关数据
      timeModal: {
        visible: false,
        startValue: '',
        endValue: '',
        hasEndTime: false,
        position: { x: 0, y: 0 },
        currentEvent: null
      },
      // 行程天数最大值
      MAX_SCHEDULE_DAYS: 15,
      editingEvent: null,
      editingContent: '',
      editModal: {
        visible: false,
        title: '编辑日程内容'
      }
    };
  },
  mounted() {
    this.initDragEvent()
    this.calendarApi = this.$refs.fullCalendar.getApi()
    window.addEventListener('resize', this.handleResize);
  },
  computed: {
    mobile() {
      return this.isMobile();
    },
    uploadUrl() {
      let url = (axios.defaults.baseURL || "") + '/file/upload'
      return url
    },
    dynamicWidth() {
      if(this.calendarOptions.resources.length>=5 && (this.screenWidth - 800 > 600)){
        return `${this.screenWidth - 800}px`;
      }else{
        return `600px`;
      }
    },
    // 获取当前模式显示文本
    getModeLabel() {
      const mode = this.modeOptions.find(m => m.value === this.editMode)
      return mode ? mode.label : '标准模式'
    },
    maxSteps() {
      switch (this.editMode) {
        case 'standard':
          return 3;
        case 'export':
        case 'h5':
          return 2;
        default:
          return 3;
      }
    },
    eventMenuStyle() {
      // 计算位置，考虑视口边界
      let { x, y } = this.eventMenu.position;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const menuWidth = 180; // 估计的菜单宽度
      const menuHeight = 130; // 估计的菜单高度
      
      // 确保菜单不超出右侧边界
      if (x + menuWidth > viewportWidth) {
        x = viewportWidth - menuWidth - 10;
      }
      
      // 确保菜单不超出底部边界
      if (y + menuHeight > viewportHeight) {
        // 如果下方空间不足，则显示在点击位置上方
        y = y - menuHeight - 10;
      }
      
      return {
        position: 'fixed',
        left: `${x}px`,
        top: `${y}px`,
        zIndex: 1100  // 设置为高于Modal的z-index(1000)
      };
    },
    
    timeModalStyle() {
      // 计算模态框位置，确保在视窗内
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const modalWidth = 300;
      const modalHeight = 150;
      
      let left = this.timeModal.position.x;
      let top = this.timeModal.position.y;
      
      if (left + modalWidth > viewportWidth) {
        left = viewportWidth - modalWidth - 20;
      }
      
      if (top + modalHeight > viewportHeight) {
        top = viewportHeight - modalHeight - 20;
      }
      
      return {
        position: 'fixed',
        left: `${left}px`,
        top: `${top}px`,
        backgroundColor: '#fff',
        borderRadius: '4px',
        padding: '20px',
        width: '300px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      }
    }
  },
  async created() {
    this.sysUser = this.$store.getters.userInfo
    this.onCloseCurrent(this.$route.path, () => {
      this.reset()
      this.$router.push({ path: "/biz/bizTrip/list" })
    })
    
    this.initCurrencyAndOptionalTitle();

    // 获取用户专属的编辑模式设置
    const userEditModeKey = `lastUsedEditMode_${this.sysUser.id}`;
    const savedEditMode = localStorage.getItem(userEditModeKey);
    
    // 如果localStorage没有存储该用户的模式,才获取公司配置的默认值
    if (!savedEditMode) {
      try {
        const companyInfo = await this.getCompanyInfoFromCache();
        const config = companyInfo && companyInfo.config ? JSON.parse(companyInfo.config) : null;
        if (config && config.defaultEditMode) {
          this.editMode = config.defaultEditMode;
        }
      } catch (error) {
        console.error('获取公司信息失败:', error);
      }
    } else {
      this.editMode = savedEditMode;
    }

    // 如果是散拼行程,强制使用标准模式
    if (this.model.type === 2) {
      this.editMode = 'standard'
    }
  },
  methods: {
    //初始化拖动事件
    initDragEvent() {
      //需要确保只初始化一次
      if (this.dragInit) {
        return
      }
      let that = this
      // 初始化外部事件
      this.dragInit = new Draggable(document.querySelector('.my-aside'), {
        itemSelector: '.fc-event',
        eventData: function (el) {
          that.isScheduleInitStatus = false; //只要有拖动，就证明用户修改了行程，点击日程天数+ -不再初始化默认日程
          let event = JSON.parse(el.getAttribute("data-event"))
          let obj = event.extendedProps
          let item = {
            id: event.id,
            title: event.title,
            backgroundColor: '#3bb2e3',
            bizId: obj.id,
            bizType: event.type,
            bizText: '介绍',
            bizDescArray: [],//用于富文本填充。图文介绍以#分割的数组
            bizAutoFillingDesc: false,
            bizRegionCity: '',//用于填充标题,例：清远2天
            bizTag: null
          }

          //不同的用户可能有相同的名字,相同的情况下，会带@ (例如 白水寨@ysg)，这里过滤掉@后面的属主名
          let titleArray = item.title.split('@')
          item.title = titleArray[0]

          if (item.bizType == "scenic") {
            if (obj.scenicTag) {
              item.bizTag = JSON.parse(obj.scenicTag)
            }
            if (obj.tripDesc) {
              //支持*分割多段,第一段用于默认的行程描述，后面的段落用于拖动，需要删除掉后面的段落
              if (obj.tripDesc.indexOf('*') >= 0) {
                item.bizText = obj.tripDesc.split('*')[0].trim()
              } else {
                item.bizText = obj.tripDesc
              }
            }
            if (obj.imgUrl) {
              item.bizImgUrl = obj.imgUrl
            }
            if (obj.advantageDesc) {
              item.bizAdvantageDesc = obj.advantageDesc
            }
            if (obj.scenicDesc) { //不能直接用 obj.scenicDescArray,没有点击景点名称前还没有生成
              //用于富文本填充
              item.bizDescArray = obj.scenicDesc.split('#')
              //删除无效元素,空或者换行之类
              item.bizDescArray = item.bizDescArray.filter(function (e) {
                return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
              })
            }
            if (obj.regionCity) {
              item.bizRegionCity = obj.regionCity //主要用于标题自动填充 例如 清远两天,方便获取城市名
            }
            //暂时没用，全部景点都是填充的
            item.bizAutoFillingDesc = true
          } else if (item.bizType == "hotel") {
            item.backgroundColor = "#FFCC00"
            if (obj.hotelTag) {
              item.bizTag = JSON.parse(obj.hotelTag)
            }
            if (obj.tripDesc) {
              item.bizText = obj.tripDesc
            }
            if (obj.imgUrl) {
              item.bizImgUrl = obj.imgUrl
            }
            if (obj.advantageDesc) {
              item.bizAdvantageDesc = obj.advantageDesc
            }
            if (obj.hotelDesc) {
              //用于富文本填充
              item.bizDescArray = obj.hotelDesc.split('#');
              //删除无效元素,空或者换行之类
              item.bizDescArray = item.bizDescArray.filter(function (e) {
                return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
              });
            }
            item.bizAutoFillingDesc = obj.autoFillingDesc
          } else if (item.bizType == "rest") {
            item.backgroundColor = "#228B22"
            if (obj.restTag) {
              item.bizTag = JSON.parse(obj.restTag)
            }
            if (obj.tripDesc) {
              item.bizText = obj.tripDesc
            }
            if (obj.restDesc) {
              //用于富文本填充
              item.bizDescArray = obj.restDesc.split('#');
              //删除无效元素,空或者换行之类
              item.bizDescArray = item.bizDescArray.filter(function (e) {
                return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
              });
            }
            item.bizAutoFillingDesc = obj.autoFillingDesc
          } else if (item.bizType == "traffic") {
            item.bizText = obj.advantageDesc
            item.backgroundColor = "#87843b"
          } else if (item.bizType == "word") {
            item.backgroundColor = "#77787b"
            //正常情况的常用语拖动
            if (!event.childTripDesc) {
              item.bizText = obj.wordDesc
            } else {//定义了子行程，有些特殊，event.extendedProps的数据是景点数据，不是这个子行程的，需要修正
              item.bizId = 0 //必须设置为0
              item.bizText = null
            }
          } else if (item.bizType == "commonFee") {
            item.bizText = obj.feeTitle
            item.backgroundColor = "#02485D"
          }
          return item
        }
      })
    },
    //完成自定义设置
    afterSetPrice(data) {
      let custItem = {
        title: data.addName,
        resourceId: "1",
        id: "cust-" + data.addName + "-1",
        bizType: data.type,
        bizPrice: data.addPrice,
        bizFeeUnit: data.feeUnit,
        bizPricePcs: data.pricePcs,
        bizCustFeePriceRemark: data.priceRemark,
        bizPriceRule: null,
      }
      //添加自定义价格标签
      this.priceTag[data.type].push(custItem)
    },
    //打开自定义价格设置modal
    openPriceModal(type) {
      this.$refs.setCustPrice.initModal(type);
      this.$refs.setCustPrice.title = '自定义费用';
    },
    // 步骤切换方法
    stepChange(type) {
      if (type === 'next') {
        if (this.step === 0) {
          let _this = this
          // 按不同模式处理第一步
          if (this.editMode === 'standard') {
            // 标准模式
            this.parseSchedule(function () {
              _this.step++
            }, function (errMsg) {
              _this.$confirm({
                content: errMsg,
                cancelText: "返回修改",
                okText: "忽略",
                okType: "danger",
                onOk() {
                  _this.step++
                }
              })
            })
          } else {
            // 快捷模式
            this.processDefaultData().then(() => {
              if (_this.editMode === 'export') {
                _this.openQuickExport()
              } else if (_this.editMode === 'h5') {
                _this.saveAndGoToAiContent()
              }
            }).catch((errMsg) => {
              _this.$confirm({
                content: errMsg,
                cancelText: "返回修改",
                okText: "忽略",
                okType: "danger",
                onOk() {
                  if (_this.editMode === 'export') {
                    _this.openQuickExport()
                  } else if (_this.editMode === 'h5') {
                    _this.saveAndGoToAiContent()
                  }
                }
              })
            })
          }
        } else {
          this.step++
        }
      } else if (type === 'prev') {
        if (this.step <= 0) {
          return
        }
        this.step--
      }
    },

    // 处理默认数据
    processDefaultData() {
      return new Promise((resolve, reject) => {
        this.parseSchedule(() => {
          // 只在新增行程时设置默认配置
          if (!this.model.id) {
            this.model.autoPrice = false // 自动报价关闭
            this.model.autoRefreshBoolean = true // 自动刷新开启
            this.model.status = "3" // 定制行程状态
          }
          resolve()
        }, (errMsg) => {
          reject(errMsg)
        })
      })
    },

    // 快捷导出模式 - 保存并打开导出弹窗
    openQuickExport() {
      // 打开导出弹窗
      this.$refs.quickExportModal.show({
        id: this.model.id,
        tripName: this.model.tripName,
        tripFullName: this.model.tripFullName,
        imgUrl: this.model.imgUrl,
        status: this.model.status,
        sysUserId: this.model.sysUserId,
        sysUserName: this.model.sysUserName,
        type: this.model.type,
        coverImgList: this.coverImgList,
        upload: {
          fileList: this.upload.fileList
        },
        // 传递获取封面图的方法
        initCoverImgList: this.initCoverImgList
      });
    },

    // 快捷H5模式 - 保存并跳转
    saveAndGoToAiContent() {
      this.save().then(() => {
        // 明确验证ID存在再跳转
        if (this.model.id) {
          this.$router.push({
            name: 'biz-bizTrip-aiContent',
            params: {
              id: this.model.id
            },
            query: {
              id: this.model.id
            }
          })
        } else {
          // ID不存在，显示错误
          this.$message.error('保存成功但获取行程ID失败，请检查')
        }
      }).catch(err => {
        this.$message.error('保存失败: ' + err.message)
      })
    },
    
    // 处理快捷导出数据更新
    handleQuickExportOk(model) {
      // 更新 model 字段
      this.model.tripName = model.tripName;
      this.model.tripFullName = model.tripFullName;
      this.model.imgUrl = model.imgUrl;

      // 同步更新图片列表
      if (this.model.imgUrl) {
        this.upload.fileList = this.model.imgUrl.split(",").map(item => ({
          "status": "done",
          "uid": item,
          "name": item,
          "url": item
        }));
      }

      // 保存到数据库
      this.save().then((res) => {
        if (res.success) {
          // 新增时,保存后需要更新 model id
          if (!this.model.id) {
            this.model.id = res.result.id;
          }

          //构造list接口查询参数
          const params = {
            id: this.model.id,
            // 添加参数表明这是精确的ID查询,提高性能
            tripIDSearch: 1
          };

          // 这里必须使用list接口,因为导出模块(exportWordModal)需要companyWordStyle和companyPptStyle等数据，get接口没有这个数据
          getAction(this.url.list, params).then(tripRes => {
            if (tripRes.success && tripRes.result.records && tripRes.result.records.length > 0) {
              // 确保 exportWordModal 存在后再调用 
              if (this.$refs.exportWordModal) {
                this.$refs.exportWordModal.show(tripRes.result.records[0]);
                // 关闭当前编辑 tab
                this.closeCurrent();
              } else {
                this.$message.error('导出组件加载失败');
              }
            } else {
              this.$message.error(tripRes.message || '获取行程数据失败');
            }
          }).catch(err => {
            this.$message.error('获取行程数据失败: ' + err.message);
          });
        } else {
          this.$message.error(res.message || '保存失败');
        }
      });
    },

    //直接点击步骤
    onChange(step) {
      // 如果点击的步骤大于最大步骤数，则不进行操作,maxSteps 在计算属性中定义
      if (step >= this.maxSteps) {
        return;
      }
      if (this.step == 0) {
        let _this = this
        // 按不同模式处理第一步
        if (this.editMode === 'standard') {
          // 标准模式
          this.parseSchedule(function () {
            _this.step = step
          }, function (errMsg) {
            _this.$confirm({
              content: errMsg,
              cancelText: "返回修改",
              okText: "忽略",
              okType: "danger",
              onOk() {
                _this.step = step
              }
            })
          })
        } else {
          // 快捷模式
          this.processDefaultData().then(() => {
            if (_this.editMode === 'export') {
              _this.openQuickExport()
            } else if (_this.editMode === 'h5') {
              _this.saveAndGoToAiContent()
            }
          }).catch((errMsg) => {
            _this.$confirm({
              content: errMsg,
              cancelText: "返回修改",
              okText: "忽略",
              okType: "danger",
              onOk() {
                if (_this.editMode === 'export') {
                  _this.openQuickExport()
                } else if (_this.editMode === 'h5') {
                  _this.saveAndGoToAiContent()
                }
              }
            })
          })
        }
      } else {
        this.step = step
      }
    },
    //点击事件弹出菜单
    clickEvent(clickInfo) {
      // 关闭已经打开的菜单
      if (this.eventMenu.visible) {
        this.closeEventMenu();
      }
      
      // 计算菜单位置，设置在鼠标点击位置下方，考虑滚动位置
      this.eventMenu.position = {
        x: clickInfo.jsEvent.clientX,
        y: clickInfo.jsEvent.clientY + 5  // 向下偏移5px，避免遮挡鼠标
      };
      
      // 存储当前事件
      this.eventMenu.currentEvent = clickInfo.event;
      
      // 显示菜单
      this.eventMenu.visible = true;
      
      // 添加全局点击和滚动事件监听
      setTimeout(() => {
        document.addEventListener('click', this.closeEventMenu);
        window.addEventListener('scroll', this.closeEventMenu);
        window.addEventListener('resize', this.closeEventMenu);
      }, 0);
    },
    // 设置鼠标悬停状态
    setItemHover(item, isHovered) {
      this.hoveredItem = isHovered ? item : null;
    },
    // 处理事件菜单的操作
    handleEventAction(action) {
      const event = this.eventMenu.currentEvent;
      
      if (!event) {
        this.closeEventMenu();
        return;
      }
      
      switch (action) {
        case 'delete':
          // 直接删除事件
          event.remove();
          break;
          
        case 'hideTime':
          // 添加hideTime属性
          const currentExtendedProps = event.extendedProps || {};
          const newExtendedProps = { ...currentExtendedProps, hideTime: true };
          
          // 更新事件样式
          this.updateEventStyle(event, newExtendedProps);
          break;
          
        case 'adjustTime':
          // 显示时间选择模态框
          this.showTimePickerModal(event);
          break;
          
        case 'edit':
          // 编辑事件
          this.editEvent(event);
          break;
      }
      
      // 关闭菜单
      this.closeEventMenu();
    },
    
    // 编辑word类型的事件内容
    editEvent(event) {
      if (!event || !event.extendedProps || event.extendedProps.bizType !== 'word') {
        return;
      }
      
      // 保存当前事件和内容到 data 中
      this.editingEvent = event;
      this.editingContent = event.title;
      
      // 显示编辑模态框
      this.editModal.visible = true;
    },
    
    // 处理编辑确认
    handleEditOk() {
      if (!this.editingContent || this.editingContent.trim() === '') {
        this.$message.warning('日程内容不能为空');
        return;
      }
      
      if (!this.editingEvent) {
        this.editModal.visible = false;
        return;
      }
      
      // 直接修改事件标题
      this.editingEvent.setProp('title', this.editingContent);
      
      // 更新事件ID(可选，仅在需要ID与标题保持一致的情况下使用)
      this.editingEvent.setProp('id', "word-" + this.editingContent);
      
      // 关闭模态框
      this.editModal.visible = false;
    },
    
    // 处理编辑取消
    handleEditCancel() {
      this.editModal.visible = false;
    },
    // 关闭事件菜单
    closeEventMenu() {
      this.eventMenu.visible = false;
      this.eventMenu.currentEvent = null;
      document.removeEventListener('click', this.closeEventMenu);
      window.removeEventListener('scroll', this.closeEventMenu);
      window.removeEventListener('resize', this.closeEventMenu);
    },
    // 更新事件样式方法
    updateEventStyle(event, newExtendedProps) {
      try {
        // 获取事件当前的所有属性
        const eventData = event.toPlainObject({collapseExtendedProps: true});
        
        // 记录事件的关键属性
        const eventId = event.id;
        const eventTitle = event.title;
        const eventStart = event.start;
        const eventEnd = event.end;
        const eventAllDay = event.allDay;
        const resources = event.getResources();
        const resourceId = resources.length > 0 ? resources[0].id : null;
        
        // 准备更新的事件数据
        const updatedEventData = {
          ...eventData,
          id: eventId,
          title: eventTitle,
          start: eventStart,
          end: eventEnd,
          allDay: eventAllDay,
          resourceId: resourceId,
          extendedProps: newExtendedProps
        };
        
        // 处理事件样式
        this.processEventStyle(updatedEventData);
        
        // 先保存旧事件的所有属性
        const oldEvent = event;
        
        // 移除旧事件
        event.remove();
        
        // 添加更新后的事件
        const newEvent = this.calendarApi.addEvent(updatedEventData);
        // 如果添加失败，恢复旧事件
        if (!newEvent) {
          console.error('无法添加更新后的事件，恢复原事件');
          this.calendarApi.addEvent(eventData);
        }
      } catch (error) {
        console.error('更新事件样式时出错:', error);
        // 尝试恢复原事件
        try {
          this.calendarApi.addEvent(event.toPlainObject());
        } catch (e) {
          console.error('恢复原事件失败:', e);
        }
      }
    },
    //个位数时间前面补0
    addZero: function (i) {
      if (i < 10) {
        i = "0" + i;
      }
      return i;
    },
    //点击日历表日期触发添加日程
    dateClick: function (selectInfo) {
      this.schedulePrompt.show = true
      let tip = "输入 " + this.addZero(selectInfo.date.getHours()) + ":" + this.addZero(selectInfo.date.getMinutes()) + " 日程，例：【特色活动】烟花#海边燃放烟花后自由活动"
      this.schedulePrompt.tip = tip
      this.schedulePrompt.event = {
        backgroundColor: "#77787b",
        bizId: 0,
        bizText: "",
        bizType: "word",
        bizWordScence: "",
        resourceId: selectInfo.resource.id,
        start: selectInfo.dateStr
      }
    },
    //添加事件后回调
    onSchedulePromptChange() {
      if (this.schedulePrompt.word.trim() == '') {
        this.$message.warning('请输入日程内容')
        return
      }
      this.schedulePrompt.event.title = this.schedulePrompt.word
      this.schedulePrompt.event.id = "word-" + this.schedulePrompt.word
      this.calendarApi.addEvent(this.schedulePrompt.event)

      //clear
      this.schedulePrompt.show = false
      this.schedulePrompt.word = ''
      this.schedulePrompt.event = {}
    },
    //添加自定义日程中的快捷键动作
    addCustItem(itemNo) {
      if (itemNo == 1) {
        this.schedulePrompt.word = "自由活动";
      }
      if (itemNo == 2) {
        this.schedulePrompt.word = "返回酒店，享受自由自在的夜生活";
      }
      if (itemNo == 3) {
        this.schedulePrompt.word = "前往入住酒店,稍作休息";
      }
      if (itemNo == 4) {
        this.schedulePrompt.word = "酒店办理入住一般为14点后,如提早到达可能无法入住,需稍作等待";
      }
      if (itemNo == 5) {
        this.schedulePrompt.word = "自由活动浸泡温泉";
      }
    },
    //日程表内拖动
    onInnerDrop() {
    },
    //新的event拖到到日程表
    onDrop(e) {
      let event = JSON.parse(e.draggedEl.getAttribute("data-event"))
      //对应schedule.js的id
      let eventId = "tip-" + event['type']
      //不管新事件拖到日程表的什么位置，直接删除同类提示事件
      let tipEvent = this.calendarApi.getEventById(eventId)
      if (tipEvent) {
        tipEvent.remove()
      }
    },
    // 打开地图展示模态框
    openTripMapModal() {
      // 直接使用当前日历中的事件数据，避免重新解析影响事件绑定
      try {
        let currentEvents = this.calendarApi.getEvents();
        let scheduleData = [];
        
        // 处理当前事件，生成地图所需的数据格式
        currentEvents.forEach(item => {
          if (item.getResources() == null || item.getResources().length == 0 || item.getResources()[0] == null) {
            return;
          }
          let resourceId = item.getResources()[0].id;
          let obj = item.toPlainObject({collapseExtendedProps: true});
          obj.resourceId = resourceId;       
          scheduleData.push(obj);
        });
        
        // 检查是否有可展示的数据（景点、酒店、餐厅、常用语）
        const mapItems = scheduleData.filter(item => 
          item.bizType === 'scenic' || 
          item.bizType === 'hotel' || 
          item.bizType === 'rest' || 
          item.bizType === 'word'
        );
        
        if (mapItems.length > 0) {
          this.$refs.tripMapModal.show(scheduleData, this.model.id);
        } else {
          this.$message.warning('请先添加景点、酒店、餐厅或常用语到行程中');
        }
      } catch (error) {
        console.error('获取地图数据失败:', error);
        this.$message.error('获取地图数据失败');
      }
    },
    //解析日程
    parseSchedule(onSuccess, onError) {
      this.$nextTick(async () => {  // 使用 async
        try {
          //老的修改原因：this.model.tripDesc 双向绑定了ueEditor编辑器。ueEditor可能有些bug，初始化会置空，导致tripDesc异常。
          //新发现：可能是ueEditor配置了autoClearinitialContent导致初始化会置空. 那增加这个backupTripDesc其实有些多余，不如直接修改autoClearinitialContent配置。
          //暂时这样，有空再琢磨吧，不然会累死
          if (this.model.backupTripDesc && !this.model.tripDesc) {
            this.model.tripDesc = this.model.backupTripDesc
          }
          //当前已经装载的事件同步为价格标签
          let currentEvents = this.calendarApi.getEvents();
          //所有由日程生成的价格标签
          let priceTag = {
            scenic: [],
            hotel: [],
            rest: [],
            traffic: [],
            commonFee: []
          }
          //天数
          this.model.dayNum = this.calendarOptions.resources.length;
          //当前事件        
          let localSchedule = []
          //记录酒店和景区id
          let scenicIds = [], hotelIds = [], restIds = [];
          //处理事件
          currentEvents.map(item => {
            //先获取并校验当前事件日期
            if (item.getResources() == null || item.getResources().length == 0 || item.getResources()[0] == null) {
              return
            }
            let resourceId = item.getResources()[0].id
            if (resourceId * 1 > this.model.dayNum) {
              return
            }
            let obj = item.toPlainObject({collapseExtendedProps: true})
            obj.resourceId = resourceId
            
            // 确保extendedProps存在
            obj.extendedProps = obj.extendedProps || {};
            
            // 将hideTime和adjustTime从extendedProps中提取出来，放到顶层
            if (item.extendedProps) {
              if (item.extendedProps.hideTime !== undefined) {
                obj.hideTime = item.extendedProps.hideTime;
              }
              if (item.extendedProps.adjustTime !== undefined) {
                obj.adjustTime = item.extendedProps.adjustTime;
              }
            }
            
            //加入最终事件列表
            localSchedule.push(obj)
            //生成价格标签
            if (obj.bizType && priceTag[obj.bizType]) {
              let priceObj = Object.assign({}, obj)
              delete priceObj["backgroundColor"];
              delete priceObj["start"];
              delete priceObj["end"];
              delete priceObj["bizText"];
              delete priceObj["bizTag"];
              priceTag[obj.bizType].push(priceObj)
            }
            let imgUrlList = []
            if (obj.bizImgUrl) {
              imgUrlList = obj.bizImgUrl.split(",");
            }
            //填充头图，第一个拖进来的景点或者酒店首图
            if (((this.upload.fileList == null) || (this.upload.fileList.length == 0))
                && (obj.bizType == 'scenic' || obj.bizType == 'hotel')
                && imgUrlList.length > 0) {
              this.upload.fileList = [{
                "status": "done",
                "uid": imgUrlList[0],
                "name": imgUrlList[0],
                "url": imgUrlList[0]
              }]
            }
            //填充标题 填充的规则是第一个拖进去的景点的城市，注意不一定是行程时间表的第一个景点。
            if (!this.model.tripName && obj.bizType == 'scenic') {
              this.model.tripName = obj.bizRegionCity + this.model.dayNum + "天"
            }

            if (obj.bizType == 'scenic') {
              scenicIds.push(obj.bizId)
            } else if (obj.bizType == 'hotel') {
              hotelIds.push(obj.bizId)
            } else if (obj.bizType == 'rest') {
              restIds.push(obj.bizId)
            }

          });
          //自动生成的素材列表，景区和酒店
          this.scenicIds = scenicIds;
          this.hotelIds = hotelIds;
          this.restIds = restIds;

          const excludeMap = {}
          this.priceTag.excludeScenic.forEach(item => {
            excludeMap[item.id] = item.id
          });
          //除了cust，都重新生成
          this.priceTag.scenic = priceTag.scenic.filter(item => (excludeMap[item.id] == null))
          this.priceTag.hotel = priceTag.hotel
          this.priceTag.traffic = priceTag.traffic
          this.priceTag.commonFee = priceTag.commonFee
          this.priceTag.rest = priceTag.rest
          //生成需要提交的日程数据
          this.model.scheduleJson = localSchedule
          this.model.schedule = JSON.stringify(localSchedule);

          //填充行程全称
          if (!this.model.tripFullName && this.priceTag.scenic) {
            let scenicNameStr = this.priceTag.scenic
              .filter(item => !item.title.startsWith('_')) // 过滤掉title以下划线开头的景点
              .map(item => item.title)
              .join("、")
            if (scenicNameStr) {
              this.model.tripFullName = scenicNameStr + " " + this.model.dayNum + "天"
            }
          }

          //将title以下划线开头的的景点放入排除列表并从景点列表中移除
          this.priceTag.scenic.forEach(item => {
            if (item.title.startsWith('_')) {
              this.priceTag.excludeScenic.push(item)
              this.priceTag.scenic = this.priceTag.scenic.filter(item => item.title.startsWith('_'))
            }
          })

          //填充行程介绍（只在标准模式，其他模式没必要了）
          if (!this.model.tripDesc && this.priceTag.scenic && this.priceTag.scenic.length > 0 && this.editMode === 'standard') {
            this.autoFill();
          }

          // 等待 autoFill 完成后再检查事件
          const errMsg = await this.checkEvent();
          if (!errMsg) {
            // 校验成功
            onSuccess && onSuccess();
          } else {
            onError && onError("缺少：" + errMsg);
          }

        } catch (error) {
          console.error('解析日程出错', error);
          onError && onError("解析日程出错");
        }
      })
    },
    //初始化封面图列表
    initCoverImgList() {
      // 如果有景区、酒店或餐厅ID，则调用后端接口获取图片
      if (this.scenicIds.length > 0 || this.hotelIds.length > 0 || this.restIds.length > 0) {
        let params = {
          "scenicIds": JSON.stringify(this.scenicIds),
          "hotelIds": JSON.stringify(this.hotelIds),
          "restIds": JSON.stringify(this.restIds)
        }
        return postAction(this.url.getCandidateCoverImages, params)
          .then(res => {
            if (res.success) {
              this.coverImgList = res.result || [];
            } else {
              console.warn('获取候选封面图列表失败:', res.message);
              this.coverImgList = [];
            }
            return this.coverImgList;
          }).catch(res => {
            console.warn('获取候选封面图列表网络错误:', res.message);
            this.coverImgList = [];
            return this.coverImgList;
          });
      } else {
        // 如果没有相关ID，则清空列表
        this.coverImgList = [];
        return Promise.resolve(this.coverImgList);
      }
    },
    // 检查日程事件
    checkEvent() {
      // 如果只有一个项目，不进行核验
      if (this.model.scheduleJson.length <= 1) {
        return Promise.resolve('');
      }

      return this.getCompanyInfoFromCache()
        .then(companyInfo => {
          const config = companyInfo && companyInfo.defaultScheduleConfig ?
            JSON.parse(companyInfo.defaultScheduleConfig) : {};
          const checkItems = config.checkItems || [];

          let errMsg = '';

          if (checkItems.includes('traffic') && !this.model.scheduleJson.some(item => item.bizType === 'traffic')) {
            errMsg += '【交通】';
          }
          if (checkItems.includes('restaurant') && !this.model.scheduleJson.some(item => item.bizType === 'rest')) {
            errMsg += '【餐厅】';
          }
          if (checkItems.includes('hotel') && this.model.dayNum > 1 && !this.model.scheduleJson.some(item => item.bizType === 'hotel')) {
            errMsg += '【酒店】';
          }
          if (checkItems.includes('scenic') && !this.model.scheduleJson.some(item => item.bizType === 'scenic')) {
            errMsg += '【景点】';
          }
          if (checkItems.includes('guide') && !this.model.scheduleJson.some(item => item.bizType === 'commonFee' && item.title.includes('导服'))) {
            errMsg += '【导服】';
          }
          if (checkItems.includes('water') && !this.model.scheduleJson.some(item => item.bizType === 'commonFee' && item.title.includes('水'))) {
            errMsg += '【水】';
          }

          return errMsg;
        })
        .catch(error => {
          console.error('检查事件出错', error);
          return '检查事件出错';
        });
    },

    //pricetag 删除
    onPriceTagClose(type, title, resourceId) {
      let item = this.priceTag[type].find((item) => (item.title == title && item.resourceId == resourceId))
      if (type == 'excludeScenic') {
        this.priceTag['scenic'].push(item)
      } else if (type == 'scenic') {
        this.priceTag['excludeScenic'].push(item)
      }
      this.priceTag[type] = this.priceTag[type].filter((item) => !(item.title == title && item.resourceId == resourceId))
    },
    //增减日程天数
    updateScheduleDay(type, day) {
      let currentDay = this.calendarOptions.resources.length;
      if (type == 'add') {
        if (currentDay >= this.MAX_SCHEDULE_DAYS) {
          this.$message.warning(`最多可添加${this.MAX_SCHEDULE_DAYS}天`);
          return
        }
        this.calendarOptions.resources.push({id: (currentDay + 1) + "", title: "第" + (currentDay + 1) + "天"})
      } else if (type == 'sub') {
        if (currentDay <= 1) {
          this.$message.warning('至少需保留1天');
          return
        }
        this.calendarOptions.resources.splice(currentDay - 1, 1)
      } else if (type == 'set' && day > 0) {
        // 确保设置的天数不超过最大限制
        if (day > this.MAX_SCHEDULE_DAYS) {
          this.$message.warning(`最多可添加${this.MAX_SCHEDULE_DAYS}天`);
          day = this.MAX_SCHEDULE_DAYS;
        }
        let resources = []
        let index = 1
        while (index <= day) {
          resources.push({id: (index) + "", title: "第" + (index) + "天"})
          index++
        }
        this.calendarOptions.resources = resources
      }
      this.getInitSchedule(this.calendarOptions.resources.length)
    },
    //时间表折叠状态控制，部分行程需要展开/折叠时间
    foldTimeline() {
      if (this.timelineFoldStatus) {
        this.timelineFoldStatus = false;
        this.calendarOptions.slotMinTime = "0:00:00";
        this.calendarOptions.slotMaxTime = "24:00:00";
      } else {
        this.timelineFoldStatus = true;
        this.calendarOptions.slotMinTime = "07:00:00";
        this.calendarOptions.slotMaxTime = "21:00:00";
      }
    },
    // 模式切换处理
    handleModeChange({ key }) {
      // 检查是否为散拼行程
      if (this.model.type === 2 && key !== 'standard') {
        this.$message.warning('散拼行程只支持标准模式')
        return
      }

      // 检查H5功能是否启用
      if (key === 'h5' && (!this.companyConfig || !this.companyConfig.enableH5)) {
        this.$message.warning('小程序H5功能未启用')
        return
      }

      // 直接切换模式，移除前面的提示确认
      this.editMode = key;
      // 保存用户专属的编辑模式设置
      const userEditModeKey = `lastUsedEditMode_${this.sysUser.id}`;
      localStorage.setItem(userEditModeKey, key);
    },
    //切换行程类型：type：2-散拼行程，其他-正常行程
    switchType() {
      if (this.model.id) {
        this.$message.warning('行程类型不能修改!')
        return
      }

      // 检查是否在快捷模式下
      if (this.editMode !== 'standard') {
        this.$message.warning('请先切换到标准模式再修改行程类型')
        return
      }

      this.model.type = this.model.type === 2 ? 1 : 2
      this.model.resetModel = this.model.type
      if (this.model.type === 2) {
        this.model.status = '1'
        this.resetModel.status = '1'
      } else {
        this.resetModel.status = '3'
      }
    },
    //修改默认正餐餐标，用于快捷按钮
    setMinRestPrice(priceLevel) {
      let that = this;
      this.$confirm({
        title: '请确认？',
        content: "将正餐最低餐标调整到：" + priceLevel,
        onOk() {
          let currentEvents = that.calendarApi.getEvents();
          currentEvents.map((item) => {
            let resourceId = item.getResources()[0].id
            let obj = item.toPlainObject({collapseExtendedProps: true})
            obj.resourceId = resourceId
            if (obj.bizType == 'rest') {
              if (commonRestBizIdArr.indexOf(obj.bizId) >= 0) {
                let restTimestamp = Date.parse(obj.start)
                let newObj = obj
                switch (priceLevel) {
                  case '350':
                    //3000-01-01T15:00:00+08:00 下午3点前为中餐,否则为晚餐
                    if (restTimestamp < 32503705200000) {
                      newObj.bizId = commonRestLunch350.bizId
                      newObj.title = commonRestLunch350.title
                      newObj.id = commonRestLunch350.id
                      that.calendarApi.addEvent(newObj)
                    } else {
                      newObj.bizId = commonRestDinner350.bizId
                      newObj.title = commonRestDinner350.title
                      newObj.id = commonRestDinner350.id
                      that.calendarApi.addEvent(newObj)
                    }
                    break;
                  case '400':
                    if (restTimestamp < 32503705200000) {
                      newObj.bizId = commonRestLunch400.bizId
                      newObj.title = commonRestLunch400.title
                      newObj.id = commonRestLunch400.id
                      that.calendarApi.addEvent(newObj)
                    } else {
                      newObj.bizId = commonRestDinner400.bizId
                      newObj.title = commonRestDinner400.title
                      newObj.id = commonRestDinner400.id
                      that.calendarApi.addEvent(newObj)
                    }
                    break;
                  case '500':
                    if (restTimestamp < 32503705200000) {
                      newObj.bizId = commonRestLunch500.bizId
                      newObj.title = commonRestLunch500.title
                      newObj.id = commonRestLunch500.id
                      that.calendarApi.addEvent(newObj)
                    } else {
                      newObj.bizId = commonRestDinner500.bizId
                      newObj.title = commonRestDinner500.title
                      newObj.id = commonRestDinner500.id
                      that.calendarApi.addEvent(newObj)
                    }
                    break;
                  default:
                    break;
                }
                //清理老的event
                that.calendarApi.getEventById(item.id).remove();
              }
            }
          })
          that.$message.info('设置成功')
        },
        onCancel() {
          return;
        },
      });
    },
    //点击候选面板，添加内容到富文本
    addTripDesc(type, data, value) {
      //只有在行程介绍步骤时才会生效。(日程step=0)
      if (this.step != 1) {
        return
      }
      if (this.currentEditor == null) {
        this.$message.error('编辑器初始化失败，请刷新后重试');
        return
      }
      if (type.indexOf("img") > -1) {
        this.currentEditor.execCommand('inserthtml', `<img width="100%" src="${value}">`);
      } else {
        this.currentEditor.execCommand('inserthtml', `<section width="100%">${value}</section>`);
      }
    },
    //初始化ueditor
    ueditorReady(editor) {
      this.currentEditor = editor;
    },
    //初始化ueditor    
    beforeReady() {
      window.UE.registerUI('135editor', function (editor, uiName) {
        var editor135;

        function onContentFrom135(event) {
          if (typeof event.data !== 'string') {
            if (event.data.ready) {
              editor135.postMessage(editor.getContent(), '*');
            }
            return;
          }
          

          if (event.data.indexOf('<') !== 0) return;

          editor.setContent(event.data);
          editor.fireEvent("catchRemoteImage");
          window.removeEventListener('message', onContentFrom135);
        }

        var btn = new window.UE.ui.Button({
          name: 'btn-dialog-' + uiName,
          className: 'edui-for-135editor',
          title: '135编辑器',
          onclick: function () {
            // 由于登录存在跨域问题，请使用如下方式调用135编辑器
            editor135 = window.open('https://www.135editor.com/beautify_editor.html?callback=true&appkey=')
            window.removeEventListener('message', onContentFrom135);
            window.addEventListener('message', onContentFrom135, false);
          }
        });
        return btn;
      }, undefined);

      window.UE.registerUI('autoFill', (editor, uiName) => {
        editor.registerCommand(uiName, {
          execCommand: function () {
          }
        });

        let btn = new window.UE.ui.Button({
          title: '自动填充',
          name: uiName,
          className: 'edui-for-autofill',
          onclick: () => {
            let that = this;
            this.$confirm({
              title: '请确认？',
              content: "清空当前介绍并自动填充",
              onOk() {
                that.autoFill()
              },
              onCancel() {
                return;
              },
            });
          }
        });
        return btn;
      });
    },
    //自动填充富文本（后端实现）
    autoFill() {
      let params = {
        "scenicIds": JSON.stringify(this.scenicIds),
        "hotelIds": JSON.stringify(this.hotelIds),
        "restIds": JSON.stringify(this.restIds)
      }
      return postAction(this.url.getRichText, params)
          .then(res => {
            if (res.success) {
              // this.$message.success(res.message || '操作成功');
              this.model.tripDesc = res.result
              //操作
            } else {
              this.$message.warning(res.message || '请求错误');
            }
          }).catch(res => {
        this.$message.warning(res.message || '网络错误');
      });
    },
    //点击城市
    onCityChange(tag, checked, obj) {
      if (checked == false) {
        return;
      }
      if (obj.dataType != null && obj.dataType == "word") {//如果是word数据
        obj.selected.scence = tag;
        obj.nameList = obj.scenceMap[tag];
        if (obj.nameList) {
          obj.nameList = obj.nameList.slice(0, 20)
        }
      } else if (obj.dataType === "hotel") {//酒店类型数据，需要多条件筛选
        obj.selected.city = tag;
        
        // 获取筛选工具
        const { filterHotels } = require('../setting/hotelFilter');
        // 应用多条件筛选
        obj.nameList = filterHotels(
          obj.nameMap, 
          obj.selected.city, 
          obj.selected.star, 
          obj.selected.priceRange, 
          obj.search
        );
        
        if (obj.nameList) {
          obj.nameList = obj.nameList.slice(0, 20)
        }
      } else {//其他类型数据
        obj.selected.city = tag;
        obj.nameList = obj.cityMap[tag];
        if (obj.nameList) {
          obj.nameList = obj.nameList.slice(0, 20)
        }
      }
    },
    //点击名称
    onNameChange(tag, checked, obj) {
      if (checked == false) {
        return;
      }
      obj.selected.name = tag;
      obj.selected.data = obj.nameMap[obj.selected.name];

      //景区介绍素材支持#分隔符
      if (obj.selected.data.scenicDesc) {
        obj.selected.data.scenicDescArray = obj.selected.data.scenicDesc.split('#');
        //删除无效元素,空或者换行之类
        obj.selected.data.scenicDescArray = obj.selected.data.scenicDescArray.filter(function (e) {
          return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
        });
      }
      //酒店介绍素材支持#分隔符
      if (obj.selected.data.hotelDesc) {
        obj.selected.data.hotelDescArray = obj.selected.data.hotelDesc.split('#');
        //删除无效元素,空或者换行之类
        obj.selected.data.hotelDescArray = obj.selected.data.hotelDescArray.filter(function (e) {
          return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
        });
      }
      //餐厅介绍素材支持#分隔符
      if (obj.selected.data.restDesc) {
        obj.selected.data.restDescArray = obj.selected.data.restDesc.split('#');
        //删除无效元素,空或者换行之类
        obj.selected.data.restDescArray = obj.selected.data.restDescArray.filter(function (e) {
          return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
        });
      }
      //子行程*切割
      if (obj.selected.data.tripDesc && obj.selected.data.tripDesc.indexOf('*') >= 0) {
        obj.selected.data.tripDescArray = obj.selected.data.tripDesc.split('*');
        //删除无效元素,空或者换行之类
        obj.selected.data.tripDescArray = obj.selected.data.tripDescArray
            .filter(function (e) {
              return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
            }) //去除空元素
            .map(function (e) {
              return e.trim()
            }); //去除首尾空行
      }
      if (obj.selected.data == null || obj.selected.data.imgUrl == null || obj.selected.data.imgUrl.length == '') {
        obj.selected.imgList = [];
      } else {
        obj.selected.imgList = obj.selected.data.imgUrl.split(",").map(item => {
          return {
            "status": "done",
            "uid": item,
            "name": item,
            "url": item
          };
        });
      }
    },
    //点击景区名称
    onCommonFeeChange(tag, checked, obj) {
      if (checked == false) {
        return
      }
      obj.selected.name = tag.feeTitle
      obj.selected.data = tag
    },
    //文件上传
    handleFileChange({fileList}) {
      if (fileList && fileList.length > 1) {
        fileList.shift()
        this.fileUpload.fileList = fileList;
        return
      }
      this.fileUpload.fileList = fileList;
    },
    //散拼行程附件最大10M
    beforeFileUpload(file) {
      const isSizeValid = file.size / 1024 / 1024 <= 10; // 10MB限制
      if (!isSizeValid) {
        this.$message.error('文件大小不能超过10MB');
      }
      return isSizeValid;
    },
    //图片上传
    handleChange({fileList}) {
      this.upload.fileList = fileList;
    },
    //图片预览
    onPreviewCancel() {
      this.showImgList = false;
    },

    onPreview() {
      this.initCoverImgList();
      this.showImgList = true;
    },
    selectImage(image) {
      const newFile = {
        uid: Date.now(),
        name: image,
        status: 'done',
        url: image,
      };
      //清空this.upload.fileList并添加新的图片
      this.upload.fileList = [];
      this.upload.fileList.push(newFile);
      this.showImgList = false;
    },
    //检查图片信息
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 <= 1;
      return new Promise((resolve, reject) => {
        if (!isLt1M) {
          this.$message.error('图片大小限制为1M!');
          reject(file);
        } else {
          resolve(file);
        }
      })
    },
    // 获取默认公司ID
    getDefaultComId() {
      return new Promise((resolve, reject) => {
        getAction(this.url.getDefaultComId)
          .then(res => {
            if (res.success) {
              this.sysUser.comId = res.result;
              resolve(res.result);
            } else {
              reject(new Error(res.message || '获取默认公司ID失败'));
            }
          })
          .catch(error => {
            console.error('获取默认公司ID出错', error);
            reject(error);
          });
      });
    },
    
    // 从缓存或服务器获取公司信息
    getCompanyInfoFromCache() {
      return new Promise((resolve, reject) => {
        const cachedData = localStorage.getItem('companyInfo_' + this.sysUser.comId);
        if (cachedData) {
          const { data, expiry } = JSON.parse(cachedData);
          if (expiry > Date.now()) {
            // 直接更新 companyConfig
            this.companyConfig = data && data.config ? JSON.parse(data.config) : null;
            // 更新 availableModeOptions
            this.updateAvailableModeOptions();
            resolve(data);
            return;
          }
          localStorage.removeItem('companyInfo_' + this.sysUser.comId);
        }

        getAction(this.url.getComanyInfo, { id: this.sysUser.comId })
          .then(res => {
            if (res.success && res.result) {
              this.setCompanyInfoToCache(res.result);
              // 更新 companyConfig
              this.companyConfig = res.result.config ? JSON.parse(res.result.config) : null;
              // 更新 availableModeOptions
              this.updateAvailableModeOptions();
              resolve(res.result);
            } else {
              reject(new Error('Failed to fetch company info'));
            }
          })
          .catch(error => {
            console.error('获取公司信息出错', error);
            reject(error);
          });
      });
    },

    // 将公司信息保存到缓存
    setCompanyInfoToCache(data) {
      const expiry = Date.now() + 3600000; // 1小时后过期
      localStorage.setItem('companyInfo_' + this.sysUser.comId, JSON.stringify({ data, expiry }));
    },

    // 初始化日程
    getInitSchedule(dayNum) {
      // 如果不是初始化状态,直接返回
      if (!this.isScheduleInitStatus) {
        return Promise.resolve();
      }

      // 重置当前日程表并设置编辑状态
      this.reset();
      this.editing = true;

      // 如果是shop b2用户，已经在activated中获取了comId
      if (!this.sysUser.comId) {
        return Promise.resolve();
      }

      return this.getCompanyInfoFromCache()
        .then(companyInfo => this.processCompanyInfo(companyInfo, dayNum))
        .catch(error => {
          console.error('获取公司信息失败', error);
          this.addBasicDefaultEvents(dayNum);
        });
    },

    // 处理公司信息并初始化日程
    processCompanyInfo(companyInfo, dayNum) {
      let config = { meals: [], journey: [], tips: [], checkItems: [], defaultSchedule: {} };

      if (companyInfo.defaultScheduleConfig) {
        config = JSON.parse(companyInfo.defaultScheduleConfig);
      }

      this.addBasicDefaultEvents(dayNum, config);

      if (config.defaultSchedule && config.defaultSchedule[dayNum]) {
        config.defaultSchedule[dayNum].forEach(item => {
          this.calendarApi.addEvent(item);
        });
      }
    },

    addBasicDefaultEvents(dayNum, config = {}) {
      const { meals = [], journey = [], tips = [] } = config;

      for (let i = 0; i < dayNum; i++) {
        // 添加午餐
        if (meals.includes('lunch')) {
          const lunch = Object.assign({}, commonRestLunch300);
          lunch.resourceId = i + 1;
          this.calendarApi.addEvent(lunch);
        }

        if (i === 0) {
          // 第一天的特殊处理
          if (journey.includes('departure')) this.calendarApi.addEvent(departTips);
          if (tips.includes('traffic')) this.calendarApi.addEvent(trafficTips);
          if (tips.includes('scenic')) this.calendarApi.addEvent(scenicTips);
          if (dayNum !== 1 && tips.includes('hotel')) this.calendarApi.addEvent(hotelTips);
        } else {
          // 非第一天添加早餐
          if (meals.includes('breakfast')) {
            const breakfast = Object.assign({}, commonRestBreakfast);
            breakfast.resourceId = i + 1;
            this.calendarApi.addEvent(breakfast);
          }
        }

        if (i !== dayNum - 1) {
          // 非最后一天添加晚餐
          if (meals.includes('dinner')) {
            const dinner = Object.assign({}, commonRestDinner300);
            dinner.resourceId = i + 1;
            this.calendarApi.addEvent(dinner);
          }
        } else if (journey.includes('end')) {
          // 最后一天添加返程事件
          const returnT = Object.assign({}, returnTips);
          returnT.resourceId = i + 1;
          // 如果只有一天，结束返程时间为17:00
          if (dayNum === 1) {
            returnT.start = "3000-01-01T17:00:00+08:00";
          }
          this.calendarApi.addEvent(returnT);
        }
      }
    },

    //编辑时加载数据
    getEditData() {
      //加载编辑数据
      getAction(this.url.get, {id: this.model.id})
          .then(res => {
            if (res.success) {
              //备份行程介绍
              this.model.backupTripDesc = res.result.tripDesc;
              if(res.result.type === 2){
                if(!res.result.tripPeriodList || res.result.tripPeriodList.length ===0){
                  res.result.tripPeriodList = [{}]
                }
                //文件回显
                if (res.result.spAttach != null && res.result.spAttach.length > 0) {
                  this.fileUpload.fileList = res.result.spAttach.split(",").map(item => {
                    return {
                      "status": "done",
                      "uid": item,
                      "name": item,
                      "url": item
                    };
                  });
                }
              }

              if (!res.result.priceRuleMap || !res.result.priceRuleMap.currency) {
                res.result.priceRuleMap = res.result.priceRuleMap || {};
                res.result.priceRuleMap.currency = localStorage.getItem('lastUsedCurrency') || this.defaultCurrency;
              }

              if (!res.result.optionalItemsTitle) {
                res.result.optionalItemsTitle = localStorage.getItem('lastUsedOptionalTitle') || '自费项目';
              }

              this.model = Object.assign(this.model, res.result);
              //图片回显
              if (this.model.imgUrl != null && this.model.imgUrl.length > 0) {
                this.upload.fileList = this.model.imgUrl.split(",").map(item => {
                  return {
                    "status": "done",
                    "uid": item,
                    "name": item,
                    "url": item
                  };
                });
              }
              //回显天数
              if (this.model.dayNum > 0) {
                this.updateScheduleDay("set", this.model.dayNum)
              }

              if (this.model.priceRule) {
                let priceRule = JSON.parse(this.model.priceRule)
                //回显自定义价格
                if (priceRule.cust) {
                  this.priceTag.cust = priceRule.cust
                }
                //回显排除景点
                if (priceRule.excludeScenic) {
                  this.priceTag.excludeScenic = priceRule.excludeScenic
                }
                //回显自动报价开关
                if (priceRule.hasOwnProperty('autoPrice')) {
                  this.model.autoPrice = priceRule.autoPrice;
                }
                //回显报价提醒
                if (priceRule.shopAutoPriceRemark) {
                  this.model.shopAutoPriceRemark = priceRule.shopAutoPriceRemark;
                }
              }
              if (this.model.type === 2) {
                this.model.priceRuleMap = JSON.parse(this.model.priceRule || '{}')
              }
              //回显自动刷新
              this.model.autoRefreshBoolean = this.model.autoRefresh == 1;
              //回显事件
              if (this.model.schedule) {
                let event = JSON.parse(this.model.schedule)
                this.loadScheduleToCalendar(event);
              }

            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
      // .catch(res => {
      //   this.$message.warning(res.message || '网络错误');
      // });
    },
    //切换左侧内容
    onTabChange(key) {
      if (key == 'scenicTab') {
        this.fetchScenicData();
      } else if (key == 'hotelTab') {
        this.fetchHotelData()
      } else if (key == 'trafficTab') {
        this.fetchTrafficData()
      } else if (key == 'restTab') {
        this.fetchRestData()
      } else if (key == 'wordTab') {
        this.fetchWordData()
      } else if (key == 'commonFeeTab') {
        this.fetchCommonFeeData()
      }
    },
    //加载常用语panel数据
    fetchWordData() {
      let url = `/biz/bizWord/wordDataList?search=${this.word.search}`
      if (this.$route.params.id) {
        url = url + "&tripId=" + this.$route.params.id
      }
      getAction(url)
          .then(res => {
            if (res.success) {
              //List<场景>
              this.word.scenceList = res.result.scenceList;
              //map<场景,List<名称>>
              this.word.scenceMap = res.result.scenceMap;
              //map<名称,List<实体>>
              this.word.nameMap = res.result.nameMap;
              //处理场景列表
              if (this.word.scenceList != null && this.word.scenceList.length > 0) {
                this.word.selected.scence = this.word.scenceList[0];
                this.word.nameList = this.word.scenceMap[this.word.selected.scence];
                if (this.word.nameList) {
                  this.word.nameList = this.word.nameList.slice(0, 20)
                }
              }
              //处理常用语列表
              if (this.word.nameList != null && this.word.nameList.length > 0) {
                this.word.selected.name = this.word.nameList[0];
                this.word.selected.data = this.word.nameMap[this.word.selected.name];
              }
            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
          .catch(res => {
            this.$message.warning(res.message || '网络错误');
          });
    },
    //加载餐厅panel数据
    fetchRestData() {
      let url = `/biz/bizRestaurant/restDataList?search=${this.rest.search}`
      if (this.$route.params.id) {
        url = url + "&tripId=" + this.$route.params.id
      }
      getAction(url)
          .then(res => {
            if (res.success) {
              //List<城市>
              this.rest.cityList = res.result.cityList;
              //map<城市,List<名称>>
              this.rest.cityMap = res.result.cityMap;
              //map<名称,List<实体>>
              this.rest.nameMap = res.result.nameMap;
              //处理城市列表
              if (this.rest.cityList != null && this.rest.cityList.length > 0) {
                this.rest.selected.city = this.rest.cityList[0];
                this.rest.nameList = this.rest.cityMap[this.rest.selected.city];
                if (this.rest.nameList) {
                  this.rest.nameList = this.rest.nameList.slice(0, 20)
                }
              }
              //处理餐厅标签列表
              if (this.rest.nameList != null && this.rest.nameList.length > 0) {
                this.rest.selected.name = this.rest.nameList[0];
                this.rest.selected.data = this.rest.nameMap[this.rest.selected.name];
                //餐厅介绍素材支持#分隔符
                if (this.rest.selected.data.restDesc) {
                  this.rest.selected.data.restDescArray = this.rest.selected.data.restDesc.split('#');
                  //删除无效元素,空或者换行之类
                  this.rest.selected.data.restDescArray = this.rest.selected.data.restDescArray.filter(function (e) {
                    return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
                  });
                }
                //行程介绍支持*分隔符
                if (this.rest.selected.data.tripDesc && this.rest.selected.data.tripDesc.indexOf('*') >= 0) {
                  this.rest.selected.data.tripDescArray = this.rest.selected.data.tripDesc.split('*');
                  //删除无效元素,空或者换行之类
                  this.rest.selected.data.tripDescArray = this.rest.selected.data.tripDescArray
                      .filter(function (e) {
                        return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
                      }) //去除空元素
                      .map(function (e) {
                        return e.trim()
                      }); //去除首尾空行
                }
              }
              //处理图片列表
              if (this.rest.selected.data != null && this.rest.selected.data.imgUrl != null && this.rest.selected.data.imgUrl.length > 0) {
                this.rest.selected.imgList = this.rest.selected.data.imgUrl.split(",").map(item => {
                  return {
                    "status": "done",
                    "uid": item,
                    "name": item,
                    "url": item
                  };
                });
              }
            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
          .catch(res => {
            this.$message.warning(res.message || '网络错误');
          });
    },
    // 地区筛选变化处理
    onScenicRegionChange(selectedRegions) {
      this.scenic.selectedRegions = selectedRegions;
      this.fetchScenicData();
    },
    
    // 系统景区数据变化处理
    onSystemScenicChange(checked) {
      this.systemScenicDataChecked = checked;
      this.fetchScenicData();
    },
    //加载景区panel数据
    fetchScenicData() {
      let url = `/biz/bizScenic/scenicDataList?search=${this.scenic.search}&systemScenicDataChecked=${this.systemScenicDataChecked}`
      
      // 添加地区筛选参数
      if (this.scenic.selectedRegions && this.scenic.selectedRegions.length > 0) {
        const regionParams = this.scenic.selectedRegions.map((region, index) => {
          let params = [];
          if (region.province) params.push(`regionProvince${index}=${encodeURIComponent(region.province)}`);
          if (region.city) params.push(`regionCity${index}=${encodeURIComponent(region.city)}`);
          if (region.area) params.push(`regionArea${index}=${encodeURIComponent(region.area)}`);
          return params.join('&');
        }).filter(p => p).join('&');
        
        if (regionParams) {
          url += '&' + regionParams;
        }
      }
      
      if (this.$route.params.id) {
        url = url + "&tripId=" + this.$route.params.id
      }
      getAction(url)
          .then(res => {
            if (res.success) {
              //List<城市>
              this.scenic.cityList = res.result.cityList;
              //map<城市,List<景区>>
              this.scenic.cityMap = res.result.cityMap;
              //map<景区,List<景区实体>>
              this.scenic.nameMap = res.result.nameMap;
              //处理城市列表
              if (this.scenic.cityList != null && this.scenic.cityList.length > 0) {
                this.scenic.selected.city = this.scenic.cityList[0];
                this.scenic.nameList = this.scenic.cityMap[this.scenic.selected.city];
                if (this.scenic.nameList) {
                  this.scenic.nameList = this.scenic.nameList.slice(0, 20)
                }
              }
              //处理景区标签列表
              if (this.scenic.nameList != null && this.scenic.nameList.length > 0) {
                this.scenic.selected.name = this.scenic.nameList[0];
                this.scenic.selected.data = this.scenic.nameMap[this.scenic.selected.name];
                //景区介绍素材支持#分隔符
                if (this.scenic.selected.data.scenicDesc) {
                  this.scenic.selected.data.scenicDescArray = this.scenic.selected.data.scenicDesc.split('#');
                  //删除无效元素,空或者换行之类
                  this.scenic.selected.data.scenicDescArray = this.scenic.selected.data.scenicDescArray.filter(function (e) {
                    return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
                  });
                }
                //行程介绍支持*分隔符
                if (this.scenic.selected.data.tripDesc && this.scenic.selected.data.tripDesc.indexOf('*') >= 0) {
                  this.scenic.selected.data.tripDescArray = this.scenic.selected.data.tripDesc.split('*');
                  //删除无效元素,空或者换行之类
                  this.scenic.selected.data.tripDescArray = this.scenic.selected.data.tripDescArray
                      .filter(function (e) {
                        return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
                      }) //去除空元素
                      .map(function (e) {
                        return e.trim()
                      }); //去除首尾空行
                }
              }
              //处理图片列表
              if (this.scenic.selected.data != null && this.scenic.selected.data.imgUrl != null && this.scenic.selected.data.imgUrl.length > 0) {
                this.scenic.selected.imgList = this.scenic.selected.data.imgUrl.split(",").map(item => {
                  return {
                    "status": "done",
                    "uid": item,
                    "name": item,
                    "url": item
                  };
                });
              }
            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
          .catch(res => {
            this.$message.warning(res.message || '网络错误');
          });
    },
    //加载酒店pannel数据
    fetchHotelData() {
      // 清空之前的筛选项
      this.hotel.selected.star = null;
      this.hotel.selected.priceRange = null;
      
      let url = `/biz/bizHotel/hotelDataList?search=${this.hotel.search}`
      if (this.$route.params.id) {
        url = url + "&tripId=" + this.$route.params.id
      }
      getAction(url)
          .then(res => {
            if (res.success) {
              //List<城市>
              this.hotel.cityList = res.result.cityList;
              //map<城市,List<景区>>
              this.hotel.cityMap = res.result.cityMap;
              //map<景区,List<景区实体>>
              this.hotel.nameMap = res.result.nameMap;
              
              // 处理星级列表和价格范围列表
              const { getStarLevelList, getPriceRangeList, groupHotelsByStar, groupHotelsByPrice } = require('../setting/hotelFilter');
              this.hotel.starLevelList = getStarLevelList(this.hotel.nameMap);
              this.hotel.priceRangeList = getPriceRangeList();
              this.hotel.starMap = groupHotelsByStar(this.hotel.nameMap);
              this.hotel.priceRangeMap = groupHotelsByPrice(this.hotel.nameMap);
              
              //处理城市列表
              if (this.hotel.cityList != null && this.hotel.cityList.length > 0) {
                this.hotel.selected.city = this.hotel.cityList[0];
                this.hotel.nameList = this.hotel.cityMap[this.hotel.selected.city];
                if (this.hotel.nameList) {
                  this.hotel.nameList = this.hotel.nameList.slice(0, 20)
                }
              }
              //处理酒店标签列表
              if (this.hotel.nameList != null && this.hotel.nameList.length > 0) {
                this.hotel.selected.name = this.hotel.nameList[0];
                this.hotel.selected.data = this.hotel.nameMap[this.hotel.selected.name];
                //酒店介绍素材支持#分隔符
                if (this.hotel.selected.data.hotelDesc) {
                  this.hotel.selected.data.hotelDescArray = this.hotel.selected.data.hotelDesc.split('#');
                  //删除无效元素,空或者换行之类
                  this.hotel.selected.data.hotelDescArray = this.hotel.selected.data.hotelDescArray.filter(function (e) {
                    return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
                  });
                }
                //行程介绍支持*分隔符
                if (this.hotel.selected.data.tripDesc && this.hotel.selected.data.tripDesc.indexOf('*') >= 0) {
                  this.hotel.selected.data.tripDescArray = this.hotel.selected.data.tripDesc.split('*');
                  //删除无效元素,空或者换行之类
                  this.hotel.selected.data.tripDescArray = this.hotel.selected.data.tripDescArray
                      .filter(function (e) {
                        return e.replace(/(\r\n|\n|\r)/gm, "") && e.trim()
                      }) //去除空元素
                      .map(function (e) {
                        return e.trim()
                      }); //去除首尾空行
                }
              }
              //处理图片列表
              if (this.hotel.selected.data != null && this.hotel.selected.data.imgUrl != null && this.hotel.selected.data.imgUrl.length > 0) {
                this.hotel.selected.imgList = this.hotel.selected.data.imgUrl.split(",").map(item => {
                  return {
                    "status": "done",
                    "uid": item,
                    "name": item,
                    "url": item
                  };
                });
              }
            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
          .catch(res => {
            this.$message.warning(res.message || '网络错误');
          });
    },
    //加载交通panel数据
    fetchTrafficData() {
      let url = `/biz/bizTraffic/trafficDataList?search=${this.traffic.search}`
      if (this.$route.params.id) {
        url = url + "&tripId=" + this.$route.params.id
      }
      getAction(url)
          .then(res => {
            if (res.success) {
              //List<城市>
              this.traffic.cityList = res.result.cityList;
              //map<城市,List<景区>>
              this.traffic.cityMap = res.result.cityMap;
              //map<景区,List<景区实体>>
              this.traffic.nameMap = res.result.nameMap;
              //处理城市列表
              if (this.traffic.cityList != null && this.traffic.cityList.length > 0) {
                this.traffic.selected.city = this.traffic.cityList[0];
                this.traffic.nameList = this.traffic.cityMap[this.traffic.selected.city];
                if (this.traffic.nameList) {
                  this.traffic.nameList = this.traffic.nameList.slice(0, 20)
                }
              }
              //处理交通标签列表
              if (this.traffic.nameList != null && this.traffic.nameList.length > 0) {
                this.traffic.selected.name = this.traffic.nameList[0];
                this.traffic.selected.data = this.traffic.nameMap[this.traffic.selected.name];
              }
            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
          .catch(res => {
            this.$message.warning(res.message || '网络错误');
          });
    },
    //加载面板通用费用
    fetchCommonFeeData() {
      let url = `/biz/bizCommonFee/commonFeeDataList`
      if (this.$route.params.id) {
        url = url + "?tripId=" + this.$route.params.id
      }
      
      // 添加搜索参数
      if (this.commonFee.search) {
        url = url + (url.includes("?") ? "&" : "?") + "search=" + this.commonFee.search
      }
      
      getAction(url)
          .then(res => {
            if (res.success) {
              this.commonFee.commonFeeList = res.result.commonFeeList
              if (this.commonFee.commonFeeList && this.commonFee.commonFeeList.length > 0) {
                this.commonFee.selected.data = this.commonFee.commonFeeList[0]
                this.commonFee.selected.name = this.commonFee.commonFeeList[0].feeTitle
              } else {
                // 如果搜索结果为空，清空选中项
                this.commonFee.selected.data = null
                this.commonFee.selected.name = null
              }

            } else {
              this.$message.warning(res.message || '请求错误');
            }
          })
          .catch(res => {
            this.$message.warning(res.message || '网络错误');
          });
    },
    //初始化加载的数据
    fetchPageData() {
      this.fetchScenicData()
    },
    // 处理保存按钮点击事件，根据步骤区分保存逻辑
    handleSave() {
      if (this.step === 0) {
        // 第0步保存：需要先执行processDefaultData检查
        return this.processDefaultData().then(() => {
          return this.save();
        }).catch((error) => {
          // 显示确认对话框让用户选择是否继续保存
          return new Promise((resolve, reject) => {
            // 处理错误信息，error可能是字符串或Error对象
            const errorMessage = typeof error === 'string' ? error : (error.message || '检查数据失败');
            
            this.$confirm({
              title: '问题确认',
              content: `${errorMessage}，是否继续保存？`,
              okText: '继续保存',
              cancelText: '取消',
              onOk: () => {
                // 用户确认继续保存
                resolve(this.save());
              },
              onCancel: () => {
                // 用户取消保存
                reject(error);
              }
            });
          });
        });
      } else {
        // 第二步及其他步骤保存：直接保存
        return this.save();
      }
    },

    //点击保存
    save() {
      //设置lastScheduleDayNum到本地存储，用于下次打开页面时默认回显天数
      localStorage.setItem("lastScheduleDayNum", this.model.dayNum)
      
      // 检查行程天数是否超过限制
      if (this.model.dayNum > this.MAX_SCHEDULE_DAYS) {
        this.$message.warning(`行程天数不能超过${this.MAX_SCHEDULE_DAYS}天`);
        return Promise.reject(new Error(`行程天数不能超过${this.MAX_SCHEDULE_DAYS}天`));
      }
      
      if(this.model.type === 2){
        //处理附件
        if(this.fileUpload.fileList && this.fileUpload.fileList.length > 0){
          this.model.spAttach = this.fileUpload.fileList.map(item => {
            if (item.url != null) {
              return item.url;
            } else if (item.response != null && item.response.success) {
              return item.response.result;
            }
          }).join(",")
        } else {
          this.model.spAttach = ""
        }
        // 团期检验
        if((!this.model.spPeriodDesc || this.model.spPeriodDesc.trim() === '')
        &&(!this.model.tripPeriodList || this.model.tripPeriodList.length ===0)){
          this.$message.warning('请设置团期!');
          return;
        }
        //检查每个团期的状态设置
        if (!this.model.spPeriodDesc || this.model.spPeriodDesc.trim() === '') {
          for (let i = 0; i < this.model.tripPeriodList.length; i++) {
            if (!this.model.tripPeriodList[i].tripDate) {
              this.$message.warning('请设置团期日期!');
              return;
            }
            if (!this.model.tripPeriodList[i].status) {
              this.$message.warning('请设置团期状态!');
              return;
            }
          }
        }
        if(!this.model.priceRuleMap){
          this.$message.warning('请设置费用信息!');
          return;
        }
        if(!this.model.priceRuleMap.adult){
          this.$message.warning('请设置成人费用!');
          return;
        }
        if(!this.model.priceRuleMap.children){
          this.$message.warning('请设置儿童费用!');
          return;
        }
        if(!this.model.spPlace){
          this.$message.warning('请设置集合点!');
          return;
        }
        this.model.priceRule = JSON.stringify(this.model.priceRuleMap);
        this.model.price = this.model.priceRuleMap.adult

        // 处理团期描述
        if (this.model.spPeriodDesc) {
          this.model.tripPeriodList = [];
        }

        // 确保自费项目标题有值
        if (!this.model.optionalItemsTitle) {
          this.model.optionalItemsTitle = this.defaultOptionalItemsTitle;
        }
        if(!this.model.priceRuleMap.currency){
          this.model.priceRuleMap.currency = this.defaultCurrency;
        }
        this.saveCurrencyAndOptionalTitle();
      }else {
        //将自动报价设置后再序列号，不然会丢失autoPrice
        //处理是否自动报价开关 
        this.priceTag.autoPrice = this.model.autoPrice;
        //处理店铺自动报价页面提醒 
        this.priceTag.shopAutoPriceRemark = this.model.shopAutoPriceRemark;
        //处理价格标签
        this.model.priceRule = JSON.stringify(this.priceTag);
      }
      //处理图片链接
      if (this.upload.fileList != null && this.upload.fileList.length > 0) {
        this.model.imgUrl = this.upload.fileList.map(item => {
          if (item.url != null) {
            return item.url;
          } else if (item.response != null && item.response.success) {
            return item.response.result;
          }
        }).join(",")
      } else {
        this.model.imgUrl = "";
        this.$message.warning('请设置行程封面图!');
        return;
      }
      if (this.model.tripName == null || this.model.tripFullName == null
          || this.model.tripName.trim().length == 0 || this.model.tripFullName.trim().length == 0) {
        this.$message.warning('请设置主副标题!');
        return;
      }
      
      //处理自动刷新
      this.model.autoRefresh = this.model.autoRefreshBoolean ? 1 : 2;

      const params = {
        ...this.model,
        needAIContent: this.editMode === 'h5' && (this.companyConfig && this.companyConfig.enableH5)
      };
      return (this.model.id ? this.edit(params) : this.add(params))
        .then(res => {
          if (res.success) {
             // 如果是新增,需要设置返回的ID
             if (!this.model.id && res.result && res.result.id) {
              this.model.id = res.result.id;
             }
             
             // 添加保存成功提示
             if (this.editMode !== 'standard') {
               this.$message.success('保存成功');
             }
             
             //如果是编辑，result为空，但是也是success
             return res;
          }
          // 如果业务逻辑失败，抛出错误
          return Promise.reject(new Error(res.message || '保存失败'));
        });
    },
    add(params) {
      this.loading = true;
      return postAction(this.url.add, params)
        .then(res => {
          if (res.success) {
            if(this.editMode === 'standard'){
              sessionStorage.setItem("newTrip", res.result.id);
              this.closeCurrent();
            }
          } else {
            this.$message.warning(res.message || '请求错误');
          }
          this.loading = false;
          return res;
        })
        .catch(err => {
          this.loading = false;
          this.$message.warning(err.message || '网络错误');
          throw err;
        });
    },

    // 修改 edit 方法
    edit(params) {
      this.loading = true;
      return putAction(this.url.edit, params)
        .then(res => {
          if (res.success) {
            if(this.editMode === 'standard'){   
              sessionStorage.setItem("newTrip", this.model.id);
              this.closeCurrent();
            }
          } else {
            this.$message.warning(res.message || '请求错误');
          }
          this.loading = false;
          return res;
        })
        .catch(err => {
          this.loading = false;
          this.$message.warning(err.message || '网络错误');
          throw err;
        });
    },
    reset() {
      this.editing = false
      this.step = 0

      this.model = Object.assign({}, this.resetModel)
      this.model.priceRuleMap = {
        adult: null,
        children: null,
        currency: this.defaultCurrency
      }
      this.upload.fileList = []
      this.priceTag.cust = []
      this.priceTag.excludeScenic = []
      this.priceTag.scenic = []
      this.priceTag.hotel = []
      this.priceTag.traffic = []
      this.priceTag.commonFee = []
      this.priceTag.rest = []
      //清空事件
      let currentEvents = this.calendarApi.getEvents();
      currentEvents.map((item) => {
        this.calendarApi.getEventById(item.id).remove()
      })
    },
    //
    handleStatus() {

    },
    /**
     * 添加团期
     */
    plusPeriod() {
      this.model.tripPeriodList.push({})
    },
    /**
     * 删除团期
     */
    minusPeriod(index) {
      this.model.tripPeriodList.splice(index, 1)
      if (this.model.tripPeriodList.length ===0) {
        this.model.tripPeriodList = [{}]
      }
    },
    initCurrencyAndOptionalTitle() {
      const savedCurrency = localStorage.getItem('lastUsedCurrency');
      if (savedCurrency && savedCurrency !== this.defaultCurrency) {
        this.defaultCurrency = savedCurrency;
      } 

      const savedOptionalTitle= localStorage.getItem('lastUsedOptionalTitle');
      if (savedOptionalTitle && savedOptionalTitle !== this.defaultOptionalItemsTitle) {
        this.defaultOptionalItemsTitle = savedOptionalTitle;
      } 
    },

    saveCurrencyAndOptionalTitle() {
      if (this.model.priceRuleMap.currency !== this.defaultCurrency) {
        localStorage.setItem('lastUsedCurrency', this.model.priceRuleMap.currency);
      } else {
        localStorage.removeItem('lastUsedCurrency');
      }

      if (this.model.optionalItemsTitle !== '自费项目') {
        localStorage.setItem('lastUsedOptionalTitle', this.model.optionalItemsTitle);
      } else {
        localStorage.removeItem('lastUsedOptionalTitle');
      }
    },
    handleResize() {
      this.screenWidth = window.innerWidth;
    },
    // 更新可用模式选项
    updateAvailableModeOptions() {
      // 1. 初始将availableModeOptions重置为modeOptions
      this.availableModeOptions = [...this.modeOptions];
      
      // 2. 如果 this.model.type === 2，只保留标准模式standard即可
      if (this.model.type === 2) {
        this.availableModeOptions = this.availableModeOptions.filter(option => option.value === 'standard');
        return;
      }
      
      // 3. 如果没有开启enableH5，则删除h5
      if (!this.companyConfig || !this.companyConfig.enableH5) {
        this.availableModeOptions = this.availableModeOptions.filter(option => option.value !== 'h5');
      }
      
      // 4. 如果companyType为1，则删除标准模式standard
      if (this.companyConfig && this.companyConfig.companyType === 1) {
        this.availableModeOptions = this.availableModeOptions.filter(option => option.value !== 'standard');
      }
    },
    handleImportOk() {
      // 根据不同的导入类型进行验证
      if (this.importType === 'fileImport' && this.importFileList.length === 0) {
        this.$message.warning('请选择一个文件上传');
        return;
      }
      
      if (this.importType === 'urlImport' && !this.importUrl) {
        this.$message.warning('请输入有效的网址');
        return;
      }
      
      if (this.importType === 'formImport') {
        if (!this.formImport.days) {
          this.$message.warning('请填写行程天数');
          return;
        }
        if (!this.formImport.destination) {
          this.$message.warning('请填写目的地');
          return;
        }
      }
      
      if (this.importType === 'chatImport' && !this.importPrompt) {
        this.$message.warning('请输入您的行程需求');
        return;
      }
      
      this.importLoading = true;
      
      // 准备发送给后端的数据
      const formData = new FormData();
      formData.append('importType', this.importType);
      
      if (this.importType === 'fileImport' && this.importFileList.length > 0) {
        formData.append('file', this.importFileList[0]);
      }
      
      if (this.importType === 'urlImport' && this.importUrl) {
        formData.append('url', this.importUrl);
      }
      
      if (this.importType === 'formImport') {
        formData.append('days', this.formImport.days);
        formData.append('departure', this.formImport.departure || '');
        formData.append('destination', this.formImport.destination || '');
        formData.append('preferredScenic', this.formImport.preferredScenic || '');
      }
      
      // 对于所有模式，如果有额外提示，都添加到请求中
      if (this.importPrompt) {
        formData.append('prompt', this.importPrompt);
      }
      
      // 调用后端API
      httpActionWithTimeout('/biz/bizTrip/importFromAi', formData, 'post', 300000)
        .then(res => {
          if (res.success) {
            this.$message.success('导入成功');

            // 根据返回的数据更新行程信息
            const tripData = res.result;

            // 这里需要设置行程天数，因为loadScheduleToCalendar需要用到
            if (tripData.dayNum) this.model.dayNum = tripData.dayNum;
            // 更新行程表
            if (tripData.schedule) {
              // 确保schedule是数组
              const scheduleData = Array.isArray(tripData.schedule)
                ? tripData.schedule
                : (typeof tripData.schedule === 'string' ? JSON.parse(tripData.schedule) : []);

              this.loadScheduleToCalendar(scheduleData).then(() => {
                this.cleanupTips();
              });

              // 更新信息必须放在loadScheduleToCalendar之后，否则会被里面的reset覆盖掉
              if (tripData.tripName) this.model.tripName = tripData.tripName;
              if (tripData.tripFullName) this.model.tripFullName = tripData.tripFullName;
              if (tripData.advantageDesc) this.model.advantageDesc = tripData.advantageDesc;
              if (tripData.imgUrl) 
              {
                this.model.imgUrl = tripData.imgUrl;
                // 转换imgUrl为文件列表对象
                this.upload.fileList = tripData.imgUrl.split(",").map(item => ({
                  "status": "done",
                  "uid": item,
                  "name": item,
                  "url": item
                }));
             }
            }

            this.importModalVisible = false;
          } else {
            // 显示详细的错误消息
            this.$message.error(res.message || '导入失败，请检查内容是否合规');
          }
        })
        .catch(err => {
          console.error('导入失败:', err);
          let errorMsg = '导入失败，请稍后重试';
          if (err.response && err.response.data && err.response.data.message) {
            errorMsg = err.response.data.message;
          }
          this.$message.error(errorMsg);
        })
        .finally(() => {
          this.importLoading = false;
        });
    },
    
    handleImportCancel() {
      this.importModalVisible = false;
    },
    
    beforeImportUpload(file) {
      // 文件类型验证
      const isValidType = [
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'application/pdf' // pdf
      ].includes(file.type);
      
      if (!isValidType) {
        this.$message.error('仅支持Word和PDF格式的文件！');
        return false;
      }
      
      // 文件大小限制为10MB
      const isLessThan10M = file.size / 1024 / 1024 < 10;
      if (!isLessThan10M) {
        this.$message.error('文件大小不能超过10MB！');
        return false;
      }
      
      // 清除之前的文件，只保留当前上传的文件
      this.importFileList = [file];
      
      // 返回false，阻止默认上传行为，由我们手动处理上传
      return false;
    },
    
    /**
     * 将日程数据加载到日历控件
     * @param {Array} scheduleEvents 日程事件数组
     * @returns {Promise} 返回Promise对象，可使用then方法确保加载完成后再执行后续操作
     * @note 注意：此方法会静默处理内部错误，不会向外抛出异常。错误发生时不会触发Promise的reject，
     * 这意味着调用时不需要catch，但也意味着错误将被隐藏，可能难以调试问题。
     */
    loadScheduleToCalendar(scheduleEvents) {
      if (!scheduleEvents || !Array.isArray(scheduleEvents) || scheduleEvents.length === 0) {
        return Promise.resolve(); // 对于无效数据，直接返回已解决的Promise
      }
      
      // 清空当前日历中的所有事件
      this.calendarApi.getEvents().forEach(event => event.remove());
      this.updateScheduleDay('set', this.model.dayNum);
      
      // 返回Promise，同时保持原有功能
      return new Promise(resolve => {
        // 等待DOM更新
        this.$nextTick(() => {
          // 添加事件到日历
          scheduleEvents.forEach(item => {
            let eventData;
            if (item.bizType) { // 新版
              eventData = {...item};
            } else { // 老版本
              eventData = {
                title: item.title,
                start: item.start,
                id: item.id,
                allDay: item.id && (item.id.startsWith("traffic") || item.id.startsWith("commonFee")),
                resourceId: item.resourceId,
                extendedProps: item.extendedProps
              };
            }
            
            // 处理事件样式
            eventData = this.processEventStyle(eventData);
            
            this.calendarApi.addEvent(eventData);
          });
          
          resolve(); // 完成后解决Promise
        });
      });
    },
    
    openImportModal() {
      // 重置导入相关的数据
      this.importFileList = [];
      this.importUrl = '';
      this.importPrompt = '';
      this.importType = 'chatImport'; // 设置默认导入类型为聊天
      this.importModalVisible = true;
    },
    showDayManager() {
      this.dayManager.visible = true;
    },
    insertDayBefore(index) {
      if (this.calendarOptions.resources.length >= this.MAX_SCHEDULE_DAYS) {
        this.$message.warning(`最多可添加${this.MAX_SCHEDULE_DAYS}天`);
        return;
      }
      this.calendarOptions.resources.splice(index, 0, { id: (index + 1) + "", title: "第" + (index + 1) + "天" });
      // 更新之后的天数ID和标题
      this.updateDaysOrder();
      // 更新事件的关联日期
      this.updateEventsResourceId(index, 'insertDayBefore');
    },
    insertDayAfter(index) {
      if (this.calendarOptions.resources.length >= this.MAX_SCHEDULE_DAYS) {
        this.$message.warning(`最多可添加${this.MAX_SCHEDULE_DAYS}天`);
        return;
      }
      this.calendarOptions.resources.splice(index + 1, 0, { id: (index + 2) + "", title: "第" + (index + 2) + "天" });
      // 更新之后的天数ID和标题
      this.updateDaysOrder();
      // 更新事件的关联日期
      this.updateEventsResourceId(index, 'insertDayAfter');
    },
    removeDay(index) {
      if (this.calendarOptions.resources.length <= 1) {
        this.$message.warning('至少需保留1天');
        return;
      }
      
      // 获取当前日期的事件
      const dayId = this.calendarOptions.resources[index].id;
      const eventsToRemove = this.calendarApi.getEvents().filter(event => {
        const resources = event.getResources();
        return resources.length > 0 && resources[0].id === dayId;
      });
      
      // 询问用户是否确认删除该天及其所有事件
      this.$confirm({
        title: '确认删除',
        content: `确定要删除第${index + 1}天及其${eventsToRemove.length}个事件吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // 删除该天的所有事件
          eventsToRemove.forEach(event => event.remove());
          
          // 删除该天
          this.calendarOptions.resources.splice(index, 1);
          
          // 更新之后的天数ID和标题
          this.updateDaysOrder();
          
          // 更新事件的关联日期
          this.updateEventsResourceId(index, 'removeDay');
          
          this.$message.success('删除成功');
          
          // 更新model中的天数
          this.model.dayNum = this.calendarOptions.resources.length;
        }
      });
    },
    // 更新天数ID和标题
    updateDaysOrder() {
      for (let i = 0; i < this.calendarOptions.resources.length; i++) {
        this.calendarOptions.resources[i].id = (i + 1) + "";
        this.calendarOptions.resources[i].title = "第" + (i + 1) + "天";
      }
    },
    // 更新事件的资源关联
    updateEventsResourceId(index, operationType) {
      const events = this.calendarApi.getEvents();
      
      if (operationType === 'removeDay') {
        // 删除天数后，将索引后面的事件resourceId减1
        events.forEach(event => {
          const resources = event.getResources();
          if (resources.length === 0) return;
          
          const resourceId = parseInt(resources[0].id);
          // 如果事件在被删除的日期之后，需要将resourceId减1
          if (resourceId > index + 1) {
            const newResourceId = (resourceId - 1) + "";
            // 直接使用资源ID字符串，而不是资源对象
            event.setResources([newResourceId]);
          }
        });
      } else if (operationType === 'insertDayBefore') {
        // 在指定位置前插入，将索引及之后的事件resourceId加1
        events.forEach(event => {
          const resources = event.getResources();
          if (resources.length === 0) return;
          
          const resourceId = parseInt(resources[0].id);
          // 如果事件在插入位置或之后，需要将resourceId加1
          if (resourceId >= index + 1) {
            const newResourceId = (resourceId + 1) + "";
            // 直接使用资源ID字符串，而不是资源对象
            event.setResources([newResourceId]);
          }
        });
      } else if (operationType === 'insertDayAfter') {
        // 在指定位置后插入，将索引之后的事件resourceId加1
        events.forEach(event => {
          const resources = event.getResources();
          if (resources.length === 0) return;
          
          const resourceId = parseInt(resources[0].id);
          // 如果事件在插入位置之后，需要将resourceId加1
          if (resourceId > index + 1) {
            const newResourceId = (resourceId + 1) + "";
            // 直接使用资源ID字符串，而不是资源对象
            event.setResources([newResourceId]);
          }
        });
      }
    },
    // 新增拖拽开始处理
    handleDragStart() {
      try {
        // 保存拖拽前的天数ID顺序
        const resourceIds = this.calendarOptions.resources.map(res => res.id);
        this.dayManager.originalIds = JSON.parse(JSON.stringify(resourceIds));
      } catch (error) {
        console.error('[handleDragStart] 保存原始天数ID时出错:', error);
      }
    },
    
    // 新增拖拽结束处理
    handleDragEnd(evt) {
      const oldIndex = evt.oldIndex;
      const newIndex = evt.newIndex;
      
      // 如果位置没有变化，不执行任何操作
      if (oldIndex === newIndex) {
        return;
      }
      
      // 确保有原始ID记录
      if (!this.dayManager.originalIds) {
        console.error('[handleDragEnd] 错误：找不到原始天数ID记录');
        this.updateDaysOrder(); // 仍然更新天数顺序
        return;
      }
      
      try {
        // 更新天数的ID和标题
        this.updateDaysOrder();
        
        // 更新事件的关联
        this.updateEventsResourceIdAfterDrag(oldIndex, newIndex);
        
        // 操作完成后清除原始ID记录
        this.dayManager.originalIds = null;
        
        this.$message.success('天数顺序已更新');
      } catch (error) {
        console.error('[handleDragEnd] 更新天数顺序时出错:', error);
        this.$message.error('更新天数顺序失败，请查看控制台日志');
      }
    },
    
    // 新增拖拽后更新事件关联
    updateEventsResourceIdAfterDrag(oldIndex, newIndex) {
      try {
        // 获取所有事件
        const events = this.calendarApi.getEvents();
        
        // 原始ID数组
        const originalIds = this.dayManager.originalIds;
        
        // 创建拖拽前后的天数ID映射关系
        const idMapping = {};
        
        // 计算拖拽后的天数ID映射关系
        if (oldIndex < newIndex) {
          // 向下拖动
          // 从oldIndex+1到newIndex的天数，都要向上移动一位
          for (let i = oldIndex + 1; i <= newIndex; i++) {
            idMapping[originalIds[i]] = originalIds[i-1];
          }
          // 被拖动的天数移动到目标位置
          idMapping[originalIds[oldIndex]] = originalIds[newIndex];
        } else {
          // 向上拖动
          // 从newIndex到oldIndex-1的天数，都要向下移动一位
          for (let i = newIndex; i < oldIndex; i++) {
            idMapping[originalIds[i]] = originalIds[i+1];
          }
          // 被拖动的天数移动到目标位置
          idMapping[originalIds[oldIndex]] = originalIds[newIndex];
        }
        
        // 处理所有事件的resourceId更新
        let updatedCount = 0;
        events.forEach((event) => {
          const resources = event.getResources();
          if (resources.length === 0) {
            return;
          }
          
          const currentResourceId = resources[0].id;
          
          // 如果当前资源ID在映射表中存在对应关系，则更新
          if (idMapping[currentResourceId]) {
            const newResourceId = idMapping[currentResourceId];
            event.setResources([newResourceId]);
            updatedCount++;
          }
        });
      } catch (error) {
        console.error('[updateEventsResourceIdAfterDrag] 更新事件关联时出错:', error);
      }
    },
    handleImportTypeChange(key) {
      // 导入类型切换
      this.importType = key;
      
      // 如果选择了文件导入，确保清空文件列表
      if (key === 'fileImport') {
        this.importFileList = [];
      }
    },
    // 清理AI导入的后的冗余内容，不太准确，但是比没有感觉好些。
    cleanupTips() {
        // 使用this.calendarApi替代之前的方式
        if (!this.calendarApi) {
            console.log('日历实例不存在，退出cleanupTips');
            return;
        }
        
        const events = this.calendarApi.getEvents();
        
        // 遍历所有事件，移除符合条件的Tips
        events.forEach(event => {
            const eventData = event.extendedProps;
            // 条件1：bizId为0且id以tip-开头的Tips，同时要求bizType为word
            const isTip = eventData.bizId === 0 && event.id.startsWith('tip-') 
                && eventData.bizType === 'tip';
            
            // 条件2：集合出发和结束返程，同时要求bizType为word
            const isSpecialTip = (eventData.bizId === departTips.bizId || eventData.bizId === returnTips.bizId) 
                && eventData.bizType === 'word';
            
            if (isTip || isSpecialTip) {
                event.remove();
            }
        });
    },
    // 显示时间选择模态框
    showTimePickerModal(event) {
      // 存储当前事件和位置信息
      this.timeModal.currentEvent = event;
      this.timeModal.position = this.eventMenu.position;
      
      // 检查是否有结束时间
      this.timeModal.hasEndTime = event.end != null;
      
      // 如果已经有调整过的时间,从adjustTime中解析
      if (event.extendedProps && event.extendedProps.adjustTime) {
        const adjustTimes = event.extendedProps.adjustTime.split('-');
        this.timeModal.startValue = adjustTimes[0];
        if (adjustTimes.length > 1) {
          this.timeModal.endValue = adjustTimes[1];
        }
      } else {
        // 设置默认时间值
        const eventStart = new Date(event.start);
        this.timeModal.startValue = `${eventStart.getHours().toString().padStart(2, '0')}:${eventStart.getMinutes().toString().padStart(2, '0')}`;
        
        if (event.end) {
          const eventEnd = new Date(event.end);
          this.timeModal.endValue = `${eventEnd.getHours().toString().padStart(2, '0')}:${eventEnd.getMinutes().toString().padStart(2, '0')}`;
        }
      }
      
      // 显示模态框
      this.timeModal.visible = true;
    },
    
    closeTimeModal() {
      this.timeModal.visible = false;
    },
    
    confirmTimeAdjust() {
      if (this.timeModal.currentEvent) {
        const currentExtendedProps = this.timeModal.currentEvent.extendedProps || {};
        
        // 将开始和结束时间编码到adjustTime字段
        let adjustTimeValue = this.timeModal.startValue;
        if (this.timeModal.hasEndTime && this.timeModal.endValue) {
          adjustTimeValue += '-' + this.timeModal.endValue;
        }
        
        const newExtendedProps = { 
          ...currentExtendedProps, 
          adjustTime: adjustTimeValue 
        };
        
        // 更新事件样式
        this.updateEventStyle(this.timeModal.currentEvent, newExtendedProps);
      }
      this.closeTimeModal();
    },
    onStarChange(tag, checked, obj) {
      if (checked == false) {
        obj.selected.star = null;
        // 重新加载城市筛选的列表
        obj.nameList = obj.cityMap[obj.selected.city];
        return;
      }
      obj.selected.star = tag;
      // 获取筛选工具
      const { filterHotels } = require('../setting/hotelFilter');
      // 应用多条件筛选
      obj.nameList = filterHotels(
        obj.nameMap, 
        obj.selected.city, 
        obj.selected.star, 
        obj.selected.priceRange, 
        obj.search
      );
      if (obj.nameList) {
        obj.nameList = obj.nameList.slice(0, 20);
      }
    },
    onPriceRangeChange(tag, checked, obj) {
      if (checked == false) {
        obj.selected.priceRange = null;
        // 重新加载城市筛选的列表
        obj.nameList = obj.cityMap[obj.selected.city];
        return;
      }
      obj.selected.priceRange = tag;
      // 获取筛选工具
      const { filterHotels } = require('../setting/hotelFilter');
      // 应用多条件筛选
      obj.nameList = filterHotels(
        obj.nameMap, 
        obj.selected.city, 
        obj.selected.star, 
        obj.selected.priceRange, 
        obj.search
      );
      if (obj.nameList) {
        obj.nameList = obj.nameList.slice(0, 20);
      }
    },
    // 处理事件样式
    processEventStyle(eventData) {
      // 检查事件本身或其extendedProps中是否有hideTime或adjustTime
      if (eventData.hideTime || eventData.adjustTime || 
          (eventData.extendedProps && (eventData.extendedProps.hideTime || eventData.extendedProps.adjustTime))) {
        // 添加边框或修改背景色以区分  
        eventData.borderColor = '#ff4d4f';  // 红色边框
        eventData.textColor = '#000';       // 黑色文本
        
        // 处理hideTime
        if (eventData.hideTime || (eventData.extendedProps && eventData.extendedProps.hideTime)) {
          eventData.hideTime = true;
          eventData.borderStyle = 'dashed';
        }
        
        // 处理adjustTime 
        if (eventData.adjustTime || (eventData.extendedProps && eventData.extendedProps.adjustTime)) {
          eventData.adjustTime = eventData.adjustTime || eventData.extendedProps.adjustTime;
          eventData.borderStyle = 'dotted';
        }
      }
      return eventData;
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    // 确保清理所有事件监听器
    document.removeEventListener('click', this.closeEventMenu);
  }
};
</script>

<style lang="scss">
/* 手机端 */
.mobile-page {
  position: relative;
  overflow-x: scroll;
}

.mobile-page .my-aside {
  width: 300px;
}

.mobile-page .my-aside .ant-card-body {
  padding-left: 10px;
}

.mobile-page .my-aside .ant-tabs-tab {
  padding: 0 5px;
}

// 表格高度
/* .mobile-page .ant-form-item-control{
  line-height: 24px;
} */
// .mobile-page .schedule-cell{
//   height: 20px;
// }
.mobile-page .my-trip-content {
  left: 310px;
  position: relative;
  min-height: 100vh;
}

/**pc端 */
.pc-page {
  position: relative;
}

.my-aside {
  width: 500px;
}

.pc-page .my-trip-content {
  left: 510px;
  position: relative;
  min-height: 1000px;
}

/* AI导入按钮样式 */
.ai-import-btn {
  position: relative;
  background: linear-gradient(to right, rgba(54, 207, 201, 0.05), rgba(54, 207, 201, 0.1));
  border: 1px dashed #36CFC9;
  overflow: hidden;
  transition: all 0.3s ease;
}

.ai-import-btn::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  transform: rotate(45deg);
  animation: ai-btn-shine 3s ease-in-out infinite;
}

.ai-import-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(54, 207, 201, 0.2);
  border-color: #36CFC9;
  color: #36CFC9;
}

.ai-robot-icon {
  animation: ai-robot-pulse 2s infinite;
}

@keyframes ai-btn-shine {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

@keyframes ai-robot-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
}

/* AI导入模态框样式 */
.ai-import-modal {
  .ant-modal-content {
    border-radius: 8px;
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }
  
  .ant-modal-header {
    background: linear-gradient(to right, #f0f6ff, #f8f9fa);
    border-bottom: 1px solid rgba(54, 207, 201, 0.1);
    padding: 16px 24px;
  }
  
  .ant-modal-title {
    color: #36CFC9;
    font-weight: 600;
    position: relative;
    padding-left: 10px;
  }
  
  .ant-modal-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(to bottom, #36CFC9, #5cdbd3);
    border-radius: 2px;
  }
  
  .ant-modal-footer {
    border-top: 1px solid rgba(54, 207, 201, 0.1);
    background: linear-gradient(to right, #f0f6ff, #f8f9fa);
  }
  
  .ant-btn-primary {
    background: linear-gradient(to right, #36CFC9, #5cdbd3);
    border: none;
    box-shadow: 0 2px 6px rgba(54, 207, 201, 0.2);
    transition: all 0.3s;
  }
  
  .ant-btn-primary:hover {
    background: linear-gradient(to right, #5cdbd3, #87e8de);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(54, 207, 201, 0.3);
  }
}

/* AI模态框内部样式 */
.ai-modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  
  .ai-modal-header-line {
    height: 1px;
    flex: 1;
    background: linear-gradient(to right, rgba(54, 207, 201, 0), rgba(54, 207, 201, 0.3), rgba(54, 207, 201, 0));
  }
  
  .ai-modal-header-text {
    margin: 0 16px;
    color: rgba(54, 207, 201, 0.8);
    font-size: 12px;
    letter-spacing: 1px;
  }
}

.ai-tabs {
  .ant-tabs-bar {
    border-bottom: 1px solid rgba(54, 207, 201, 0.1);
  }
  
  .ant-tabs-tab {
    transition: all 0.3s;
  }
  
  .ant-tabs-tab-active {
    color: #36CFC9;
    font-weight: 500;
  }
  
  .ant-tabs-ink-bar {
    background-color: #36CFC9;
    height: 3px;
    border-radius: 1.5px;
  }
}

.ai-textarea, .ai-input, .ai-input-number {
  border-radius: 4px;
  border: 1px solid rgba(54, 207, 201, 0.2);
  transition: all 0.3s;
  
  &:hover, &:focus {
    border-color: #36CFC9;
    box-shadow: 0 0 0 2px rgba(54, 207, 201, 0.1);
  }
}

.ai-voice-icon {
  animation: ai-voice-pulse 2s ease-in-out infinite;
}

@keyframes ai-voice-pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 自适应响应式样式 */
@media (max-width: 768px) {
  .ai-modal-header-text {
    font-size: 10px;
  }
}

.time-modal-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-modal-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 15px;
}

.time-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 15px;
}

.button-container {
  display: flex;
  justify-content: flex-end;
}

.cancel-btn {
  margin-right: 8px;
  padding: 5px 15px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
}

.confirm-btn {
  padding: 5px 15px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  background-color: #1890ff;
  color: #fff;
  cursor: pointer;
}

.time-input-group {
  margin-bottom: 15px;
}

.time-input-item {
  margin-bottom: 10px;
  
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
  }
  
  .time-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
  }
}
</style>
<style lang="scss" scoped>
.my-form-item-upload {
  width: 600px;
}

.form {
  padding: 0 0 0 0;
}

.content-width-Fixed {
  overflow-y: hidden;
}

.my-form-item {
  display: flex;

  textarea {
    width: 500px;
  }

  input {
    width: 500px;
  }

  .btn-left {
    margin-left: 20px;
  }

  a-cascader {
    width: 200px;
  }
}

.checkbox-area {
  width: 600px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.my-center {
  text-align: center;

  .btn-left {
    margin-left: 20px;
  }
}

.title {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  margin: 20px 0 10px;
  align-items: center;

  .spot {
    width: 6px;
    height: 16px;
    margin: 0 8px 0 0;
    background: #5ac78a;
    border-radius: 6px;
  }
}

.ImgModal {
  ::v-deep .ant-modal-body {
    padding: 24px !important;
  }

  ::v-deep .ant-modal-close {
    .ant-modal-close-x {
      display: block;
      width: 36px;
      height: 36px;
      font-size: 16px;
      font-style: normal;
      line-height: 36px;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
    }
  }
}

.pannel-text-block {
  border: #999 1px dashed;
  margin: 10px 0;
  font-size: 12px;
  cursor: pointer;
  // white-space: pre-line;
}

.pannel-text-block-border {
  border: #13C2C2 1px solid;
  margin: 10px 0;
  font-size: 12px;
  cursor: pointer;
  // white-space: pre-line;
}

.pannel-img-block {
  width: 100%;
  object-fit: cover;
  border: 1px dashed #999;
  margin: 10px 0;
  cursor: pointer;
}

.edui-for-135editor {
  background-image: url("http://static.135editor.com/img/icons/editor-135-icon.png") !important;
  background-size: 85%;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 999;
}

.fc-header-toolbar {
  display: none !important;
}

.ant-layout {
  overflow-y: hidden;
}

.fc-event {
  cursor: pointer;
}

.scattered-trip-box {
  margin-top: 10px;
  border: #dfdfdf solid 2px;
  width: 600px;
  min-height: 200px;
  padding: 10px;
}

.scattered-trip-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.box-line {
  border: #13C2C2 1px dashed;
  margin: 20px 0 20px 0;
}

.date-box {
  margin-bottom: 10px;
}

.date-box-item {
  width: 170px !important;
  margin-right: 8px
}

.price-item {
  width: 130px;
  margin-right: 8px
}

.price-textarea {
  //margin-top: 20px;
  width: 550px;
}

.attach-box {
  margin: 20px 0;
}
</style>
<style>

.my-trip-content .ant-upload-select-picture-card {
  width: 200px;
  height: 200px;
}

.my-trip-content .ant-upload-list-item-list-type-picture-card {
  width: 200px;
  height: 200px;
}


/* 进度条样式 */
.step-progress .custom-steps .ant-steps-item {
  cursor: pointer;
  transition: all 0.3s;
}

.step-progress .custom-steps .ant-steps-item .ant-steps-item-icon {
  position: relative;
}

.step-progress .custom-steps .ant-steps-item .ant-steps-item-icon::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #1890ff;
  border-radius: 50%;
  animation: breathing 3s ease-in-out infinite;
  opacity: 0;
}

.step-progress .custom-steps .ant-steps-item:hover .ant-steps-item-icon {
  transform: scale(1.1);
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
}

.step-progress .custom-steps .ant-steps-item:hover .ant-steps-item-icon::before {
  animation: none;
  opacity: 0.2;
}

.step-progress .custom-steps .ant-steps-item:hover .step-title {
  color: #1890ff;
  font-weight: 500;
  transform: translateY(-2px);
}

.step-progress .custom-steps .ant-steps-item-active .ant-steps-item-icon {
  animation: gentle-shake 6s ease-in-out infinite;
}

.step-progress .custom-steps .ant-steps-item-active .ant-steps-item-icon::before {
  animation: breathing 2s ease-in-out infinite;
  opacity: 0.3;
}

.step-progress .custom-steps .ant-steps-item-finish .ant-steps-item-icon {
  border-color: #1890ff;
}

.step-progress .custom-steps .step-title {
  transition: all 0.3s;
}

/* 呼吸光环动画 */
@keyframes breathing {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

/* 轻微抖动动画 */
@keyframes gentle-shake {
  0%, 100% {
    transform: translateX(0);
  }
  1%, 3% {
    transform: translateX(-2px);
  }
  2%, 4% {
    transform: translateX(2px);
  }
  5% {
    transform: translateX(0);
  }
}
/* 进度条样式结束 */


/* 天数管理器样式 */
.day-manager-container {
  max-height: 400px;
  overflow-y: auto;
}

.day-manager-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.day-manager-item:hover {
  background-color: #e6f7ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.day-title {
  font-weight: bold;
  font-size: 16px;
}

.day-actions {
  display: flex;
  gap: 8px;
}

.day-actions .ant-btn {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}


/* 拖拽样式 */
.drag-handle {
  cursor: move;
  margin-right: 8px;
  color: #1890ff;
}

.day-manager-item-ghost {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #1890ff;
}

.day-manager-item-chosen {
  background: #e6f7ff;
  border: 1px solid #1890ff;
}

/* 天数管理器样式结束 */

</style>
